{"name": "express-skeleton-3", "version": "1.0.0", "description": "A Skeleton Application", "engines": {"node": ">=14"}, "scripts": {"start": "npm run serve", "serve": "nodemon ./bin/www", "debug": "nodemon --inspect ./bin/www", "lint": "npm run lint:quiet", "lint:quiet": "eslint --quiet './**/*.js'", "lint:all": "eslint './**/*.js'", "lint:fix": "eslint './**/*.js' --quiet --fix", "test:nocoverage": "ava", "test:withcoverage": "nyc ava", "test": "npm run test:withcoverage", "gendoc:apidoc": "apidoc -i routes/ -o public/docs", "gendoc": "node ./swagger.js"}, "dependencies": {"@aws-sdk/client-acm": "^3.803.0", "@aws-sdk/client-cloudfront": "^3.803.0", "@aws-sdk/client-route-53": "^3.803.0", "@aws-sdk/client-route-53-domains": "^3.803.0", "@aws-sdk/client-waf": "^3.803.0", "@google/generative-ai": "^0.24.1", "agenda": "^4.2.1", "aws-sdk": "^2.1692.0", "axios": "^1.9.0", "bcrypt": "^5.0.1", "body-parser": "^2.2.0", "cookie-parser": "~1.4.4", "cors": "^2.8.5", "cuid": "^2.1.8", "debug": "~2.6.9", "dotenv": "^16.0.0", "ejs": "~2.6.1", "email-templates": "^8.0.10", "express": "~4.16.1", "express-jwt": "^6.1.1", "express-rate-limit": "^7.5.0", "helmet": "^5.0.2", "http-errors": "~1.6.3", "is-valid-domain": "^0.1.6", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "mongodb-memory-server": "^8.4.1", "mongoose": "^6.2.6", "morgan": "~1.9.1", "randomstring": "^1.2.2", "stripe": "^18.1.0", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1"}, "eslintIgnore": ["public/", "bin/"], "devDependencies": {"apidoc": "^0.17.7", "ava": "^4.1.0", "eslint": "^8.10.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.25.4", "faker": "^6.6.6", "husky": "^7.0.4", "nodemon": "^2.0.15", "nyc": "^15.1.0"}, "husky": {"hooks": {"pre-push": "npm run lint:quiet"}}}