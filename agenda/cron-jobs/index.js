module.exports = async (agenda) => {
  // Cron job for calculating months of experience of a candidate
  await agenda.cancel({ name: "demo" })
  await agenda.create("demo")
    .repeatEvery("1 day")
    .schedule("11:59pm")
    .save()

  // Cron job for checking subscription expiry reminders
  await agenda.cancel({ name: "check-subscription-expiry" })
  await agenda.create("check-subscription-expiry")
    .repeatEvery("1 day")
    .schedule("8:00am")
    .save()
}
