// Import job modules
const paymentJobs = require("./payment-jobs")
const reminderJobs = require("./reminder-jobs")

module.exports = (agenda) => {
  agenda.define("demo", async (job) => {
    try {
      job.remove()
    } catch (error) {
      console.log(`Agenda => ${error}`)
      job.remove()
    }
  })

  // Load payment-related jobs
  paymentJobs(agenda)

  // Load reminder-related jobs
  reminderJobs(agenda)
}
