/* eslint-disable max-len */
const paymentProcessor = require("../lib/payment-processor")

// Revenue share and fee constants from environment variables are now moved to the processor module

module.exports = (agenda) => {
  /**
   * Process successful payment intent
   * This job will be scheduled when a payment_intent.succeeded webhook is received
   * and will run after 4 months to handle the transition from trial to paid subscription
   */
  agenda.define("process-successful-payment", async (job) => {
    try {
      const { userId, subscriptionId } = job.attrs.data

      // Process the payment using the extracted function and pass the agenda instance
      paymentProcessor.processSubscriptionPayment(userId, subscriptionId, agenda)

      return job.remove()
    } catch (error) {
      console.error("Error processing payment job:", error)
      return job.remove()
    }
  })
}
