/* eslint-disable max-len */
const { Platform, Subscription, User } = require("../models")
const mail = require("../lib/mail")

/**
 * Send profile completion reminder emails to users
 * This job will check for platforms that have recently changed to launch status 1 or 2
 * and send a reminder email to their owners the next day
 * Emails are only sent in production environment
 */
module.exports = (agenda) => {
  // Define the job to check for platforms that need reminders
  agenda.define("check-platform-status", async () => {
    try {
      // Find all platforms with launch status 1 or 2 that were updated in the last 24 hours
      const oneDayAgo = new Date()
      oneDayAgo.setDate(oneDayAgo.getDate() - 1)

      const platforms = await Platform.find({
        launchStatus: { $in: [1, 2] },
        updatedAt: { $gte: oneDayAgo }
      }).lean()

      // Schedule reminder emails for each platform for tomorrow
      if (platforms.length > 0) {
        // Schedule the reminder job for tomorrow at 10:00 AM
        const tomorrow = new Date()
        tomorrow.setDate(tomorrow.getDate() + 1)
        tomorrow.setHours(10, 0, 0, 0)

        // Store platform IDs for the reminder job
        const platformIds = platforms.map((platform) => platform._id.toString())

        await agenda.schedule(tomorrow, "send-profile-reminder", { platformIds })
      }

      // Schedule the next check for tomorrow
      const tomorrow = new Date()
      tomorrow.setDate(tomorrow.getDate() + 1)
      tomorrow.setHours(9, 0, 0, 0) // Run at 9:00 AM, before the reminder job

      await agenda.schedule(tomorrow, "check-platform-status")
    } catch (error) {
      // Even if there's an error, schedule the next check
      const tomorrow = new Date()
      tomorrow.setDate(tomorrow.getDate() + 1)
      tomorrow.setHours(9, 0, 0, 0)

      await agenda.schedule(tomorrow, "check-platform-status")
    }
  })

  // Define the job to send reminder emails
  agenda.define("send-profile-reminder", async (job) => {
    try {
      const { platformIds } = job.attrs.data || { platformIds: [] }

      if (!platformIds || platformIds.length === 0) {
        return
      }



      // Find platforms with their primary users
      const platforms = await Platform.find({
        _id: { $in: platformIds }
      }).populate("primaryUser", "email name").lean()

      // Send reminder email to each platform owner
      platforms.forEach((platform) => {
        if (!platform.primaryUser || !platform.primaryUser.email) {
          return
        }

        const user = platform.primaryUser

        // Get user's first name
        const firstName = user.name && user.name.first ? user.name.first : "User"

        // Send the actual email with error handling
        try {
          mail("profile-reminder", {
            to: user.email,
            subject: "Let's Get Your Platform Ready 🎯",
            locals: {
              firstName,
              url: `${process.env.SITE_URL}/login`,
              platform: "Vewmee White Level"
            }
          });
          console.log(`Profile reminder email sent to ${user.email}`);
        } catch (emailError) {
          console.error(`Error sending profile reminder email to ${user.email}:`, emailError);
          // Continue execution even if email fails
        }
      });
    } catch (error) {
      // Handle error silently
    }
  })

  /**
   * Check for subscriptions that are expiring in 7, 3, or 1 days
   * This job runs daily to identify subscriptions that need reminder emails
   */
  agenda.define("check-subscription-expiry", async () => {
    try {
      const now = new Date()

      // Calculate dates for 7, 3, and 1 days from now
      const sevenDaysFromNow = new Date(now)
      sevenDaysFromNow.setDate(now.getDate() + 7)
      sevenDaysFromNow.setHours(23, 59, 59, 999) // End of day

      const threeDaysFromNow = new Date(now)
      threeDaysFromNow.setDate(now.getDate() + 3)
      threeDaysFromNow.setHours(23, 59, 59, 999)

      const oneDayFromNow = new Date(now)
      oneDayFromNow.setDate(now.getDate() + 1)
      oneDayFromNow.setHours(23, 59, 59, 999)

      // Start of each day for comparison
      const sevenDaysStart = new Date(sevenDaysFromNow)
      sevenDaysStart.setHours(0, 0, 0, 0)

      const threeDaysStart = new Date(threeDaysFromNow)
      threeDaysStart.setHours(0, 0, 0, 0)

      const oneDayStart = new Date(oneDayFromNow)
      oneDayStart.setHours(0, 0, 0, 0)

      // Find subscriptions expiring in exactly 7, 3, or 1 days
      const subscriptionsToRemind = await Subscription.find({
        status: "active",
        model: "setup-fee", // Only for 3-month trial subscriptions
        $or: [
          { endDate: { $gte: sevenDaysStart, $lte: sevenDaysFromNow } },
          { endDate: { $gte: threeDaysStart, $lte: threeDaysFromNow } },
          { endDate: { $gte: oneDayStart, $lte: oneDayFromNow } }
        ]
      }).populate("user", "email name")
        // .populate("_platform", "name")
        .lean()

      // Schedule reminder emails for each subscription
      for (const subscription of subscriptionsToRemind) {
        if (!subscription.user || !subscription.user.email) {
          continue
        }

        // Calculate days left
        const timeDiff = new Date(subscription.endDate) - now
        const daysLeft = Math.ceil(timeDiff / (1000 * 60 * 60 * 24))

        // Only send reminders for exactly 7, 3, or 1 days
        if ([7, 3, 1].includes(daysLeft)) {
          // Schedule the reminder email to be sent immediately
          await agenda.now("send-subscription-reminder", {
            subscriptionId: subscription._id.toString(),
            userId: subscription.user._id.toString(),
            daysLeft,
            // platformName: subscription._platform?.name || "Your Platform"
          })
        }
      }

      // Schedule the next check for tomorrow at 8:00 AM
      const tomorrow = new Date()
      tomorrow.setDate(tomorrow.getDate() + 1)
      tomorrow.setHours(8, 0, 0, 0)

      await agenda.schedule(tomorrow, "check-subscription-expiry")
    } catch (error) {
      console.error("Error in check-subscription-expiry job:", error)
      // Even if there's an error, schedule the next check
      const tomorrow = new Date()
      tomorrow.setDate(tomorrow.getDate() + 1)
      tomorrow.setHours(8, 0, 0, 0)

      await agenda.schedule(tomorrow, "check-subscription-expiry")
    }
  })

  /**
   * Send subscription expiry reminder email
   * This job sends the actual reminder email to users
   */
  agenda.define("send-subscription-reminder", async (job) => {
    try {
      const { subscriptionId, userId, daysLeft, platformName } = job.attrs.data || {}

      if (!subscriptionId || !userId || !daysLeft) {
        return
      }



      // Get user details
      const user = await User.findOne({ _id: userId }).lean()

      if (!user || !user.email) {
        return
      }

      // Get user's first name
      const firstName = user.name && user.name.first ? user.name.first : "User"

      // Create billing link (you may need to adjust this URL based on your frontend)
      const billingLink = `${process.env.SITE_URL}/account/payments`

      // Send the subscription reminder email
      await mail("subscription-reminder", {
        to: user.email,
        subject: "Your Subscription Ends Soon – Don't Miss Out ⏳",
        locals: {
          userFirstName: firstName,
          daysLeft,
          platformName: "Vewmee White Level",
          billingLink
        }
      })

      console.log(`Subscription reminder email sent to ${user.email} for ${daysLeft} days left`)
    } catch (error) {
      console.error("Error sending subscription reminder email:", error)
    }
  })

  // Schedule the initial check job to run immediately
  agenda.on("ready", async () => {
    // Cancel any existing jobs with these names
    await agenda.cancel({ name: "check-platform-status" })
    await agenda.cancel({ name: "send-profile-reminder" })
    await agenda.cancel({ name: "check-subscription-expiry" })
    await agenda.cancel({ name: "send-subscription-reminder" })

    // Schedule the check jobs to run immediately
    await agenda.now("check-platform-status")
    await agenda.now("check-subscription-expiry")
  })
}
