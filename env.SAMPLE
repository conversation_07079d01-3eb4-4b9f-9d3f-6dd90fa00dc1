NODE_ENV="development"
SITE_URL="http://localhost:3000"
API_VERSION="1"
SECRET=""
MONGODB_CONNECTION_STRING="mongodb://localhost:27017/skeleton-3"
REDIS_CONNECTION_STRING=""
SMTP_HOST=""
SMTP_PORT=""
SMTP_FROM_ADDRESS=""
SMTP_AUTH_USER=""
SMTP_AUTH_PASSWORD=""
AWS_ACCESS_KEY=""
AWS_SECRET_KEY=""

AWS_BUCKET_FOR_PDF=""
ONESIGNAL_APPID=""
ONESIGNAL_BASE_URL=""
ONESIGNAL_API_KEY=""
GOOGLE_API_KEY=""
REPLICATE_API_TOKEN=""

# Payment configuration
REVENUE_THRESHOLD="1500"
REVENUE_SHARE_PERCENTAGE="0.10"
PROCESSING_FEE_PERCENTAGE="0.05"
FLAT_FEE="150"

# Stripe configuration
STRIPE_SECRET_KEY=""
STRIPE_PUBLISHABLE_KEY=""
EXTERNAL_STRIPE_SECRET_KEY=""
STRIPE_PLATFORM_ACCOUNT_ID=""
STRIPE_WEBHOOK_SECRET=""
PLATFORM_BANK_ACCOUNT_ID=""
PLATFORM_CARD_ID=""
