define({ "api": [
  {
    "type": "delete",
    "url": "/account/payment-method/:id",
    "title": "Delete a payment method",
    "name": "deletePaymentMethod",
    "group": "Account",
    "version": "1.0.0",
    "permission": [
      {
        "name": "User"
      }
    ],
    "success": {
      "fields": {
        "200": [
          {
            "group": "200",
            "type": "json",
            "optional": false,
            "field": "Deletion",
            "description": "<p>confirmation</p>"
          }
        ]
      }
    },
    "filename": "routes/rest/account.js",
    "groupTitle": "Account"
  },
  {
    "type": "get",
    "url": "/account",
    "title": "Get account details",
    "name": "getAccount",
    "group": "Account",
    "version": "1.0.0",
    "permission": [
      {
        "name": "User"
      }
    ],
    "success": {
      "fields": {
        "200": [
          {
            "group": "200",
            "type": "json",
            "optional": false,
            "field": "Account",
            "description": "<p>details</p>"
          }
        ]
      }
    },
    "filename": "routes/rest/account.js",
    "groupTitle": "Account"
  },
  {
    "type": "get",
    "url": "/account/payments",
    "title": "Get payment history",
    "name": "getPaymentHistory",
    "group": "Account",
    "version": "1.0.0",
    "permission": [
      {
        "name": "User"
      }
    ],
    "success": {
      "fields": {
        "200": [
          {
            "group": "200",
            "type": "json",
            "optional": false,
            "field": "Payment",
            "description": "<p>history</p>"
          }
        ]
      }
    },
    "filename": "routes/rest/account.js",
    "groupTitle": "Account"
  },
  {
    "type": "get",
    "url": "/account/payment-methods",
    "title": "Get all payment methods",
    "name": "getPaymentMethods",
    "group": "Account",
    "version": "1.0.0",
    "permission": [
      {
        "name": "User"
      }
    ],
    "success": {
      "fields": {
        "200": [
          {
            "group": "200",
            "type": "json",
            "optional": false,
            "field": "List",
            "description": "<p>of payment methods</p>"
          }
        ]
      }
    },
    "filename": "routes/rest/account.js",
    "groupTitle": "Account"
  },
  {
    "type": "put",
    "url": "/account/payment-method/:id/default",
    "title": "Set a payment method as default",
    "name": "setDefaultPaymentMethod",
    "group": "Account",
    "version": "1.0.0",
    "permission": [
      {
        "name": "User"
      }
    ],
    "success": {
      "fields": {
        "200": [
          {
            "group": "200",
            "type": "json",
            "optional": false,
            "field": "Update",
            "description": "<p>confirmation</p>"
          }
        ]
      }
    },
    "filename": "routes/rest/account.js",
    "groupTitle": "Account"
  },
  {
    "type": "put",
    "url": "/account",
    "title": "Update account details",
    "name": "updateAccount",
    "group": "Account",
    "version": "1.0.0",
    "permission": [
      {
        "name": "User"
      }
    ],
    "parameter": {
      "fields": {
        "Parameter": [
          {
            "group": "Parameter",
            "type": "String",
            "optional": false,
            "field": "firstName",
            "description": "<p>First name</p>"
          },
          {
            "group": "Parameter",
            "type": "String",
            "optional": false,
            "field": "lastName",
            "description": "<p>Last name</p>"
          },
          {
            "group": "Parameter",
            "type": "String",
            "optional": false,
            "field": "phone",
            "description": "<p>Phone number</p>"
          }
        ]
      }
    },
    "success": {
      "fields": {
        "200": [
          {
            "group": "200",
            "type": "json",
            "optional": false,
            "field": "Updated",
            "description": "<p>account details</p>"
          }
        ]
      }
    },
    "filename": "routes/rest/account.js",
    "groupTitle": "Account"
  },
  {
    "type": "put",
    "url": "/account/payment-method",
    "title": "Update payment method",
    "name": "updatePaymentMethod",
    "group": "Account",
    "version": "1.0.0",
    "permission": [
      {
        "name": "User"
      }
    ],
    "parameter": {
      "fields": {
        "Parameter": [
          {
            "group": "Parameter",
            "type": "String",
            "optional": false,
            "field": "paymentMethodId",
            "description": "<p>Stripe payment method ID</p>"
          }
        ]
      }
    },
    "success": {
      "fields": {
        "200": [
          {
            "group": "200",
            "type": "json",
            "optional": false,
            "field": "Updated",
            "description": "<p>payment method</p>"
          }
        ]
      }
    },
    "filename": "routes/rest/account.js",
    "groupTitle": "Account"
  },
  {
    "type": "get",
    "url": "/user/:id",
    "title": "get user details",
    "name": "userDetails",
    "group": "Admin_User",
    "version": "1.0.0",
    "permission": [
      {
        "name": "User"
      }
    ],
    "header": {
      "fields": {
        "Header": [
          {
            "group": "Header",
            "type": "String",
            "optional": false,
            "field": "Authorization",
            "description": "<p>The JWT Token in format &quot;Bearer xxxx.yyyy.zzzz&quot;</p>"
          }
        ]
      }
    },
    "parameter": {
      "fields": {
        "Parameter": [
          {
            "group": "Parameter",
            "type": "String",
            "optional": false,
            "field": "id",
            "description": "<p>Users unique ID.</p>"
          }
        ]
      }
    },
    "success": {
      "fields": {
        "200": [
          {
            "group": "200",
            "type": "json",
            "optional": false,
            "field": "name",
            "description": "<p>description</p>"
          }
        ]
      },
      "examples": [
        {
          "title": "Success-Response:",
          "content": "{\n  \"error\" : false,\n  \"user\" : {\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"00000000000\",\n    \"name\"  : {\n      \"first\":\"Jhon\",\n      \"last\" :\"Doe\"\n    }\n  }\n}",
          "type": "type"
        }
      ]
    },
    "filename": "routes/admin/users.js",
    "groupTitle": "Admin_User"
  },
  {
    "type": "post",
    "url": "/forgotpassword",
    "title": "3.0 Request to get password reset link in mail",
    "name": "forgotPassword",
    "group": "Auth",
    "version": "1.0.0",
    "permission": [
      {
        "name": "Public"
      }
    ],
    "parameter": {
      "fields": {
        "Parameter": [
          {
            "group": "Parameter",
            "type": "String",
            "optional": false,
            "field": "handle",
            "description": "<p>(email)</p>"
          }
        ]
      },
      "examples": [
        {
          "title": "Request-Example:",
          "content": "{\n    \"handle\" : \"<EMAIL>\"\n}",
          "type": "json"
        }
      ]
    },
    "success": {
      "fields": {
        "200": [
          {
            "group": "200",
            "type": "json",
            "optional": false,
            "field": "name",
            "description": "<p>description</p>"
          }
        ]
      },
      "examples": [
        {
          "title": "Success-Response:",
          "content": "{\n    \"error\" : false,\n    \"handle\" : \"<EMAIL>\"\n}",
          "type": "json"
        }
      ]
    },
    "filename": "routes/rest/auth/password.js",
    "groupTitle": "Auth"
  },
  {
    "type": "post",
    "url": "/signup/google",
    "title": "5.0 Google Sign Up",
    "name": "googleSignup",
    "group": "Auth",
    "version": "1.0.0",
    "permission": [
      {
        "name": "Public"
      }
    ],
    "parameter": {
      "fields": {
        "Parameter": [
          {
            "group": "Parameter",
            "type": "String",
            "optional": false,
            "field": "access_token",
            "description": "<p>Google access token</p>"
          }
        ]
      }
    },
    "success": {
      "fields": {
        "200": [
          {
            "group": "200",
            "type": "json",
            "optional": false,
            "field": "name",
            "description": "<p>description</p>"
          }
        ]
      },
      "examples": [
        {
          "title": "Success-Response:",
          "content": "{\n    \"error\" : false,\n    \"data\":{\n     \"user\": {\n      \"email\": \"<EMAIL>\",\n      \"name\": {\n        \"first\": \"John\",\n        \"last\": \"Doe\"\n      },\n     }\n      \"token\": \"authToken.abc.xyz\"\n    }\n}",
          "type": "json"
        }
      ]
    },
    "error": {
      "examples": [
        {
          "title": "Error-Response:",
          "content": "{\n    \"error\" : true,\n    \"reason\": \"No Google Access Token Found!!\"\n\n}",
          "type": "json"
        },
        {
          "title": "Error-Response:",
          "content": "{\n    \"error\" : true,\n    \"reason\": \"User already exists!\"\n}",
          "type": "json"
        }
      ]
    },
    "filename": "routes/rest/auth/signup.js",
    "groupTitle": "Auth"
  },
  {
    "type": "post",
    "url": "/resetpassword",
    "title": "4.0 Request to set a new password",
    "name": "resetPassword",
    "group": "Auth",
    "version": "1.0.0",
    "permission": [
      {
        "name": "Public"
      }
    ],
    "parameter": {
      "fields": {
        "Parameter": [
          {
            "group": "Parameter",
            "type": "String",
            "optional": false,
            "field": "token",
            "description": ""
          },
          {
            "group": "Parameter",
            "type": "String",
            "optional": false,
            "field": "password",
            "description": ""
          },
          {
            "group": "Parameter",
            "type": "String",
            "optional": false,
            "field": "email",
            "description": ""
          }
        ]
      },
      "examples": [
        {
          "title": "Request-Example:",
          "content": "{\n    \"email\" : \"<EMAIL>\"\n}",
          "type": "json"
        }
      ]
    },
    "success": {
      "fields": {
        "200": [
          {
            "group": "200",
            "type": "json",
            "optional": false,
            "field": "name",
            "description": "<p>description</p>"
          }
        ]
      },
      "examples": [
        {
          "title": "Success-Response:",
          "content": "{\n    \"error\" : false,\n    \"email\" : \"<EMAIL>\"\n}",
          "type": "json"
        }
      ]
    },
    "filename": "routes/rest/auth/password.js",
    "groupTitle": "Auth"
  },
  {
    "type": "post",
    "url": "/login",
    "title": "2.0 User login",
    "name": "userLogin",
    "group": "Auth",
    "version": "1.0.0",
    "permission": [
      {
        "name": "Public"
      }
    ],
    "parameter": {
      "fields": {
        "Parameter": [
          {
            "group": "Parameter",
            "type": "String",
            "optional": false,
            "field": "handle",
            "description": "<p>(mobile / email)</p>"
          },
          {
            "group": "Parameter",
            "type": "String",
            "optional": false,
            "field": "password",
            "description": "<p>user's password</p>"
          }
        ]
      },
      "examples": [
        {
          "title": "Request-Example:",
          "content": "{\n    \"handle\" : \"<EMAIL>\",\n    \"password\" : \"myNewPassword\"\n}",
          "type": "json"
        }
      ]
    },
    "success": {
      "fields": {
        "200": [
          {
            "group": "200",
            "type": "json",
            "optional": false,
            "field": "name",
            "description": "<p>description</p>"
          }
        ]
      },
      "examples": [
        {
          "title": "Success-Response:",
          "content": "{\n    \"error\" : false,\n    \"handle\" : \"<EMAIL>\",\n    \"token\": \"authToken.abc.xyz\"\n}",
          "type": "json"
        }
      ]
    },
    "filename": "routes/rest/auth/index.js",
    "groupTitle": "Auth"
  },
  {
    "type": "post",
    "url": "/signup",
    "title": "1.0 User registration",
    "name": "userRegistration",
    "group": "Auth",
    "version": "1.0.0",
    "permission": [
      {
        "name": "Public"
      }
    ],
    "parameter": {
      "fields": {
        "Parameter": [
          {
            "group": "Parameter",
            "type": "String",
            "optional": false,
            "field": "email",
            "description": "<p>User's email address</p>"
          },
          {
            "group": "Parameter",
            "type": "String",
            "optional": false,
            "field": "name",
            "description": "<p>User's name</p>"
          },
          {
            "group": "Parameter",
            "type": "String",
            "optional": false,
            "field": "password",
            "description": "<p>User's password</p>"
          },
          {
            "group": "Parameter",
            "type": "String",
            "optional": false,
            "field": "phone",
            "description": "<p>User's phone number</p>"
          },
          {
            "group": "Parameter",
            "type": "String",
            "optional": false,
            "field": "address",
            "description": "<p>User's address</p>"
          }
        ]
      },
      "examples": [
        {
          "title": "Request-Example:",
          "content": "{\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"00000000000\",\n  \"name\": \"John Doe\",\n  \"address\": \"123 Main St, City, Country\",\n  \"password\": \"securePassword123\"\n}",
          "type": "json"
        }
      ]
    },
    "success": {
      "fields": {
        "200": [
          {
            "group": "200",
            "type": "json",
            "optional": false,
            "field": "name",
            "description": "<p>description</p>"
          }
        ]
      },
      "examples": [
        {
          "title": "Success-Response:",
          "content": "{\n  \"error\": false,\n  \"data\": {\n    \"user\": {\n      \"email\": \"<EMAIL>\",\n      \"name\": \"John Doe\",\n      \"phone\": \"00000000000\",\n      \"address\": \"123 Main St, City, Country\"\n    }\n  },\n  \"token\": \"authToken.abc.xyz\"\n}",
          "type": "json"
        }
      ]
    },
    "filename": "routes/rest/auth/signup.js",
    "groupTitle": "Auth"
  },
  {
    "type": "get",
    "url": "/documents",
    "title": "Get document review status",
    "name": "getDocumentStatus",
    "group": "Documents",
    "version": "1.0.0",
    "permission": [
      {
        "name": "User"
      }
    ],
    "success": {
      "fields": {
        "200": [
          {
            "group": "200",
            "type": "json",
            "optional": false,
            "field": "Document",
            "description": "<p>review status</p>"
          }
        ]
      }
    },
    "filename": "routes/rest/documents.js",
    "groupTitle": "Documents"
  },
  {
    "type": "post",
    "url": "/documents/review",
    "title": "Mark documents as reviewed",
    "name": "reviewDocuments",
    "group": "Documents",
    "version": "1.0.0",
    "permission": [
      {
        "name": "User"
      }
    ],
    "parameter": {
      "fields": {
        "Parameter": [
          {
            "group": "Parameter",
            "type": "String",
            "optional": false,
            "field": "feedback",
            "description": "<p>Optional feedback on documents</p>"
          }
        ]
      }
    },
    "success": {
      "fields": {
        "200": [
          {
            "group": "200",
            "type": "json",
            "optional": false,
            "field": "Updated",
            "description": "<p>document review status</p>"
          }
        ]
      }
    },
    "filename": "routes/rest/documents.js",
    "groupTitle": "Documents"
  },
  {
    "type": "get",
    "url": "/domain",
    "title": "Get domain details",
    "name": "getDomain",
    "group": "Domain",
    "version": "1.0.0",
    "permission": [
      {
        "name": "User"
      }
    ],
    "success": {
      "fields": {
        "200": [
          {
            "group": "200",
            "type": "json",
            "optional": false,
            "field": "Domain",
            "description": "<p>details</p>"
          }
        ]
      }
    },
    "filename": "routes/rest/domain.js",
    "groupTitle": "Domain"
  },
  {
    "type": "put",
    "url": "/domain/purchase",
    "title": "Purchase domain",
    "name": "purchaseDomain",
    "group": "Domain",
    "version": "1.0.0",
    "permission": [
      {
        "name": "User"
      }
    ],
    "parameter": {
      "fields": {
        "Parameter": [
          {
            "group": "Parameter",
            "type": "String",
            "optional": false,
            "field": "name",
            "description": "<p>Domain name to purchase</p>"
          },
          {
            "group": "Parameter",
            "type": "String",
            "optional": false,
            "field": "paymentMethodId",
            "description": "<p>Stripe payment method ID</p>"
          }
        ]
      }
    },
    "success": {
      "fields": {
        "200": [
          {
            "group": "200",
            "type": "json",
            "optional": false,
            "field": "Purchase",
            "description": "<p>result</p>"
          }
        ]
      }
    },
    "filename": "routes/rest/domain.js",
    "groupTitle": "Domain"
  },
  {
    "type": "post",
    "url": "/domain/setup",
    "title": "Setup domain",
    "name": "setupDomain",
    "group": "Domain",
    "version": "1.0.0",
    "permission": [
      {
        "name": "User"
      }
    ],
    "parameter": {
      "fields": {
        "Parameter": [
          {
            "group": "Parameter",
            "type": "String",
            "optional": false,
            "field": "name",
            "description": "<p>Domain name</p>"
          },
          {
            "group": "Parameter",
            "type": "String",
            "optional": false,
            "field": "provider",
            "description": "<p>Domain provider</p>"
          }
        ]
      }
    },
    "success": {
      "fields": {
        "200": [
          {
            "group": "200",
            "type": "json",
            "optional": false,
            "field": "Updated",
            "description": "<p>domain details</p>"
          }
        ]
      }
    },
    "filename": "routes/rest/domain.js",
    "groupTitle": "Domain"
  },
  {
    "type": "post",
    "url": "/domain/verify",
    "title": "Verify domain",
    "name": "verifyDomain",
    "group": "Domain",
    "version": "1.0.0",
    "permission": [
      {
        "name": "User"
      }
    ],
    "success": {
      "fields": {
        "200": [
          {
            "group": "200",
            "type": "json",
            "optional": false,
            "field": "Verification",
            "description": "<p>result</p>"
          }
        ]
      }
    },
    "filename": "routes/rest/domain.js",
    "groupTitle": "Domain"
  },
  {
    "type": "get",
    "url": "/launch",
    "title": "Get launch status",
    "name": "getLaunchStatus",
    "group": "Launch",
    "version": "1.0.0",
    "permission": [
      {
        "name": "User"
      }
    ],
    "success": {
      "fields": {
        "200": [
          {
            "group": "200",
            "type": "json",
            "optional": false,
            "field": "Launch",
            "description": "<p>status</p>"
          }
        ]
      }
    },
    "filename": "routes/rest/launch.js",
    "groupTitle": "Launch"
  },
  {
    "type": "post",
    "url": "/launch/confirm",
    "title": "Launch website",
    "name": "launchWebsite",
    "group": "Launch",
    "version": "1.0.0",
    "permission": [
      {
        "name": "User"
      }
    ],
    "success": {
      "fields": {
        "200": [
          {
            "group": "200",
            "type": "json",
            "optional": false,
            "field": "Launch",
            "description": "<p>confirmation</p>"
          }
        ]
      }
    },
    "filename": "routes/rest/launch.js",
    "groupTitle": "Launch"
  },
  {
    "type": "post",
    "url": "/launch/review",
    "title": "Review website before launch",
    "name": "reviewWebsite",
    "group": "Launch",
    "version": "1.0.0",
    "permission": [
      {
        "name": "User"
      }
    ],
    "parameter": {
      "fields": {
        "Parameter": [
          {
            "group": "Parameter",
            "type": "String",
            "optional": false,
            "field": "feedback",
            "description": "<p>Optional feedback</p>"
          }
        ]
      }
    },
    "success": {
      "fields": {
        "200": [
          {
            "group": "200",
            "type": "json",
            "optional": false,
            "field": "Updated",
            "description": "<p>launch status</p>"
          }
        ]
      }
    },
    "filename": "routes/rest/launch.js",
    "groupTitle": "Launch"
  },
  {
    "type": "post",
    "url": "/add/card",
    "title": "Add a new card to user's Stripe account",
    "name": "addCard",
    "group": "Payment",
    "version": "1.0.0",
    "permission": [
      {
        "name": "Authenticated"
      }
    ],
    "header": {
      "fields": {
        "Header": [
          {
            "group": "Header",
            "type": "String",
            "optional": false,
            "field": "Authorization",
            "description": "<p>The JWT Token in format &quot;Bearer xxxx.yyyy.zzzz&quot;</p>"
          }
        ]
      }
    },
    "parameter": {
      "fields": {
        "Parameter": [
          {
            "group": "Parameter",
            "type": "String",
            "optional": false,
            "field": "cardToken",
            "description": "<p>Stripe card token</p>"
          }
        ]
      }
    },
    "success": {
      "examples": [
        {
          "title": "Success-Response:",
          "content": "{\n  \"error\": false,\n  \"data\": {\n    \"card\": {\n      \"id\": \"card_1...\",\n      \"last4\": \"4242\",\n      \"brand\": \"visa\",\n      \"exp_month\": 12,\n      \"exp_year\": 2025\n    }\n  }\n}",
          "type": "json"
        }
      ]
    },
    "filename": "routes/rest/payment.js",
    "groupTitle": "Payment"
  },
  {
    "type": "post",
    "url": "/card/default",
    "title": "Set default card for a user",
    "name": "addDefaultCard",
    "group": "Payment",
    "version": "1.0.0",
    "permission": [
      {
        "name": "Authenticated"
      }
    ],
    "header": {
      "fields": {
        "Header": [
          {
            "group": "Header",
            "type": "String",
            "optional": false,
            "field": "Authorization",
            "description": "<p>The JWT Token in format &quot;Bearer xxxx.yyyy.zzzz&quot;</p>"
          }
        ]
      }
    },
    "parameter": {
      "fields": {
        "Parameter": [
          {
            "group": "Parameter",
            "type": "String",
            "optional": false,
            "field": "token",
            "description": "<p>Stripe card token</p>"
          }
        ]
      }
    },
    "success": {
      "examples": [
        {
          "title": "Success-Response:",
          "content": "{\n  \"error\": false,\n  \"data\": {\n    \"card\": {\n      \"id\": \"card_1...\",\n      \"last4\": \"4242\",\n      \"brand\": \"visa\",\n      \"exp_month\": 12,\n      \"exp_year\": 2025,\n      \"is_default\": true\n    }\n  }\n}",
          "type": "json"
        }
      ]
    },
    "filename": "routes/rest/payment.js",
    "groupTitle": "Payment"
  },
  {
    "type": "get",
    "url": "/fetch/card",
    "title": "Fetch all cards for a user",
    "name": "fetchCards",
    "group": "Payment",
    "version": "1.0.0",
    "permission": [
      {
        "name": "Authenticated"
      }
    ],
    "header": {
      "fields": {
        "Header": [
          {
            "group": "Header",
            "type": "String",
            "optional": false,
            "field": "Authorization",
            "description": "<p>The JWT Token in format &quot;Bearer xxxx.yyyy.zzzz&quot;</p>"
          }
        ]
      }
    },
    "success": {
      "examples": [
        {
          "title": "Success-Response:",
          "content": "{\n  \"error\": false,\n  \"data\": {\n    \"cards\": [\n      {\n        \"id\": \"card_1...\",\n        \"last4\": \"4242\",\n        \"brand\": \"visa\",\n        \"exp_month\": 12,\n        \"exp_year\": 2025,\n        \"is_default\": true\n      }\n    ]\n  }\n}",
          "type": "json"
        }
      ]
    },
    "filename": "routes/rest/payment.js",
    "groupTitle": "Payment"
  },
  {
    "type": "post",
    "url": "/subscription/setup-fee",
    "title": "Pay setup fee and start subscription",
    "name": "paySetupFee",
    "group": "Payment",
    "version": "1.0.0",
    "permission": [
      {
        "name": "Authenticated"
      }
    ],
    "header": {
      "fields": {
        "Header": [
          {
            "group": "Header",
            "type": "String",
            "optional": false,
            "field": "Authorization",
            "description": "<p>The JWT Token in format &quot;Bearer xxxx.yyyy.zzzz&quot;</p>"
          }
        ]
      }
    },
    "success": {
      "examples": [
        {
          "title": "Success-Response:",
          "content": "{\n  \"error\": false,\n  \"data\": {\n    \"launchStatus\": \"subscription\"\n  }\n}",
          "type": "json"
        }
      ]
    },
    "error": {
      "examples": [
        {
          "title": "Error-Response:",
          "content": "{\n  \"error\": true,\n  \"reason\": \"Setup fee already paid\"\n}",
          "type": "json"
        }
      ]
    },
    "filename": "routes/rest/payment.js",
    "groupTitle": "Payment"
  },
  {
    "type": "post",
    "url": "/platform/comments",
    "title": "Add a comment",
    "name": "addComment",
    "group": "Platform",
    "version": "1.0.0",
    "permission": [
      {
        "name": "User"
      }
    ],
    "header": {
      "fields": {
        "Header": [
          {
            "group": "Header",
            "type": "String",
            "optional": false,
            "field": "Authorization",
            "description": "<p>The JWT Token in format &quot;Bearer xxxx.yyyy.zzzz&quot;</p>"
          }
        ]
      }
    },
    "parameter": {
      "fields": {
        "Parameter": [
          {
            "group": "Parameter",
            "type": "String",
            "optional": false,
            "field": "section",
            "description": "<p>Section (website, fan-app, creator-app)</p>"
          },
          {
            "group": "Parameter",
            "type": "String",
            "optional": false,
            "field": "content",
            "description": "<p>Comment content</p>"
          },
          {
            "group": "Parameter",
            "type": "String",
            "optional": false,
            "field": "parentComment",
            "description": "<p>Parent comment ID (optional)</p>"
          }
        ]
      }
    },
    "success": {
      "fields": {
        "200": [
          {
            "group": "200",
            "type": "json",
            "optional": false,
            "field": "Created",
            "description": "<p>comment</p>"
          }
        ]
      }
    },
    "filename": "routes/rest/platform.js",
    "groupTitle": "Platform"
  },
  {
    "type": "get",
    "url": "/platform/comments",
    "title": "Get comments",
    "name": "getComments",
    "group": "Platform",
    "version": "1.0.0",
    "permission": [
      {
        "name": "User"
      }
    ],
    "header": {
      "fields": {
        "Header": [
          {
            "group": "Header",
            "type": "String",
            "optional": false,
            "field": "Authorization",
            "description": "<p>The JWT Token in format &quot;Bearer xxxx.yyyy.zzzz&quot;</p>"
          }
        ]
      }
    },
    "parameter": {
      "fields": {
        "Parameter": [
          {
            "group": "Parameter",
            "type": "String",
            "optional": false,
            "field": "section",
            "description": "<p>Section (website, fan-app, creator-app)</p>"
          }
        ]
      }
    },
    "success": {
      "fields": {
        "200": [
          {
            "group": "200",
            "type": "json",
            "optional": false,
            "field": "Comments",
            "description": ""
          }
        ]
      }
    },
    "filename": "routes/rest/platform.js",
    "groupTitle": "Platform"
  },
  {
    "type": "put",
    "url": "/platform",
    "title": "Update platform details",
    "name": "updatePlatform",
    "group": "Platform",
    "version": "1.0.0",
    "permission": [
      {
        "name": "User"
      }
    ],
    "header": {
      "fields": {
        "Header": [
          {
            "group": "Header",
            "type": "String",
            "optional": false,
            "field": "Authorization",
            "description": "<p>The JWT Token in format &quot;Bearer xxxx.yyyy.zzzz&quot;</p>"
          }
        ]
      }
    },
    "parameter": {
      "fields": {
        "Parameter": [
          {
            "group": "Parameter",
            "type": "String",
            "optional": false,
            "field": "name",
            "description": "<p>Platform name (same as platformName from signup)</p>"
          },
          {
            "group": "Parameter",
            "type": "String",
            "optional": false,
            "field": "colorPalette",
            "description": "<p>Color palette</p>"
          },
          {
            "group": "Parameter",
            "type": "String",
            "optional": false,
            "field": "logo",
            "description": "<p>Logo URL</p>"
          },
          {
            "group": "Parameter",
            "type": "Object",
            "optional": false,
            "field": "socialProfiles",
            "description": "<p>Social profiles</p>"
          },
          {
            "group": "Parameter",
            "type": "String",
            "optional": false,
            "field": "ideaDescription",
            "description": "<p>Idea description</p>"
          },
          {
            "group": "Parameter",
            "type": "String",
            "optional": false,
            "field": "existingWebsite",
            "description": "<p>Existing website</p>"
          },
          {
            "group": "Parameter",
            "type": "String",
            "optional": false,
            "field": "specialRequirements",
            "description": "<p>Special requirements</p>"
          },
          {
            "group": "Parameter",
            "type": "Array",
            "optional": false,
            "field": "teamMembers",
            "description": "<p>Team members (use /platform/team endpoint instead)</p>"
          }
        ]
      }
    },
    "success": {
      "fields": {
        "200": [
          {
            "group": "200",
            "type": "json",
            "optional": false,
            "field": "Updated",
            "description": "<p>platform details</p>"
          }
        ]
      }
    },
    "filename": "routes/rest/platform.js",
    "groupTitle": "Platform"
  },
  {
    "type": "post",
    "url": "/stripe/connect",
    "title": "Connect Stripe account",
    "name": "connectStripe",
    "group": "Stripe",
    "version": "1.0.0",
    "permission": [
      {
        "name": "User"
      }
    ],
    "parameter": {
      "fields": {
        "Parameter": [
          {
            "group": "Parameter",
            "type": "String",
            "optional": false,
            "field": "accountId",
            "description": "<p>Stripe account ID</p>"
          }
        ]
      }
    },
    "success": {
      "fields": {
        "200": [
          {
            "group": "200",
            "type": "json",
            "optional": false,
            "field": "Updated",
            "description": "<p>Stripe access details</p>"
          }
        ]
      }
    },
    "filename": "routes/rest/stripe.js",
    "groupTitle": "Stripe"
  },
  {
    "type": "post",
    "url": "/stripe/create-connect-account",
    "title": "Create a new Stripe Connect account",
    "name": "createConnectAccount",
    "group": "Stripe",
    "version": "1.0.0",
    "permission": [
      {
        "name": "User"
      }
    ],
    "success": {
      "fields": {
        "200": [
          {
            "group": "200",
            "type": "json",
            "optional": false,
            "field": "Account",
            "description": "<p>creation details with onboarding link</p>"
          }
        ]
      }
    },
    "filename": "routes/rest/stripe.js",
    "groupTitle": "Stripe"
  },
  {
    "type": "get",
    "url": "/stripe/account-status/:accountId",
    "title": "Get Stripe Connect account status",
    "name": "getConnectAccountStatus",
    "group": "Stripe",
    "version": "1.0.0",
    "permission": [
      {
        "name": "User"
      }
    ],
    "success": {
      "fields": {
        "200": [
          {
            "group": "200",
            "type": "json",
            "optional": false,
            "field": "Account",
            "description": "<p>status details</p>"
          }
        ]
      }
    },
    "filename": "routes/rest/stripe.js",
    "groupTitle": "Stripe"
  },
  {
    "type": "get",
    "url": "/stripe",
    "title": "Get Stripe access details",
    "name": "getStripeAccess",
    "group": "Stripe",
    "version": "1.0.0",
    "permission": [
      {
        "name": "User"
      }
    ],
    "success": {
      "fields": {
        "200": [
          {
            "group": "200",
            "type": "json",
            "optional": false,
            "field": "Stripe",
            "description": "<p>access details</p>"
          }
        ]
      }
    },
    "filename": "routes/rest/stripe.js",
    "groupTitle": "Stripe"
  },
  {
    "type": "get",
    "url": "/subscription/payments",
    "title": "Get payment history",
    "name": "getPaymentHistory",
    "group": "Subscription",
    "version": "1.0.0",
    "permission": [
      {
        "name": "User"
      }
    ],
    "success": {
      "fields": {
        "200": [
          {
            "group": "200",
            "type": "json",
            "optional": false,
            "field": "Payment",
            "description": "<p>history</p>"
          }
        ]
      }
    },
    "filename": "routes/rest/subscription.js",
    "groupTitle": "Subscription"
  },
  {
    "type": "get",
    "url": "/subscription",
    "title": "Get subscription details",
    "name": "getSubscription",
    "group": "Subscription",
    "version": "1.0.0",
    "permission": [
      {
        "name": "User"
      }
    ],
    "success": {
      "fields": {
        "200": [
          {
            "group": "200",
            "type": "json",
            "optional": false,
            "field": "Subscription",
            "description": "<p>details</p>"
          }
        ]
      }
    },
    "filename": "routes/rest/subscription.js",
    "groupTitle": "Subscription"
  },
  {
    "type": "put",
    "url": "/subscription/model",
    "title": "Update subscription model",
    "name": "updateSubscriptionModel",
    "group": "Subscription",
    "version": "1.0.0",
    "permission": [
      {
        "name": "User"
      }
    ],
    "parameter": {
      "fields": {
        "Parameter": [
          {
            "group": "Parameter",
            "type": "String",
            "optional": false,
            "field": "model",
            "description": "<p>Subscription model (revenue-share, flat-fee)</p>"
          }
        ]
      }
    },
    "success": {
      "fields": {
        "200": [
          {
            "group": "200",
            "type": "json",
            "optional": false,
            "field": "Updated",
            "description": "<p>subscription details</p>"
          }
        ]
      }
    },
    "filename": "routes/rest/subscription.js",
    "groupTitle": "Subscription"
  },
  {
    "type": "post",
    "url": "/addMembers",
    "title": "1.0 Add a team member",
    "name": "addTeamMember",
    "group": "User",
    "version": "1.0.0",
    "permission": [
      {
        "name": "User"
      }
    ],
    "header": {
      "fields": {
        "Header": [
          {
            "group": "Header",
            "type": "String",
            "optional": false,
            "field": "Authorization",
            "description": "<p>The JWT Token in format &quot;Bearer xxxx.yyyy.zzzz&quot;</p>"
          }
        ]
      }
    },
    "parameter": {
      "fields": {
        "Parameter": [
          {
            "group": "Parameter",
            "type": "String",
            "optional": false,
            "field": "name",
            "description": "<p>Full name of the team member (will be split into first and last name)</p>"
          },
          {
            "group": "Parameter",
            "type": "String",
            "optional": false,
            "field": "email",
            "description": "<p>Email of the team member</p>"
          },
          {
            "group": "Parameter",
            "type": "String",
            "optional": false,
            "field": "phone",
            "description": "<p>Phone number of the team member</p>"
          },
          {
            "group": "Parameter",
            "type": "String",
            "optional": false,
            "field": "address",
            "description": "<p>Address of the team member</p>"
          },
          {
            "group": "Parameter",
            "type": "String",
            "optional": true,
            "field": "imageUrl",
            "description": "<p>Profile image URL of the team member</p>"
          }
        ]
      },
      "examples": [
        {
          "title": "Request-Example:",
          "content": "{\n  \"name\": \"John Doe\",\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"1234567890\",\n  \"address\": \"123 Main St, City, Country\",\n  \"imageUrl\": \"https://example.com/profile.jpg\"\n}",
          "type": "json"
        }
      ]
    },
    "success": {
      "fields": {
        "Success 200": [
          {
            "group": "Success 200",
            "type": "Boolean",
            "optional": false,
            "field": "error",
            "description": "<p>Error flag</p>"
          },
          {
            "group": "Success 200",
            "type": "String",
            "optional": false,
            "field": "message",
            "description": "<p>Success message</p>"
          },
          {
            "group": "Success 200",
            "type": "Object",
            "optional": false,
            "field": "teamMember",
            "description": "<p>Created team member</p>"
          }
        ]
      },
      "examples": [
        {
          "title": "Success-Response:",
          "content": "{\n  \"error\": false,\n  \"message\": \"Team member added successfully\",\n  \"teamMember\": {\n    \"id\": \"60d21b4667d0d8992e610c85\",\n    \"email\": \"<EMAIL>\",\n    \"name\": {\n      \"first\": \"John\",\n      \"last\": \"Doe\"\n    },\n    \"phone\": \"1234567890\"\n  }\n}",
          "type": "json"
        }
      ]
    },
    "filename": "routes/rest/users.js",
    "groupTitle": "User"
  },
  {
    "type": "put",
    "url": "/teamMember/:id",
    "title": "4.0 Delete a team member",
    "name": "deleteTeamMember",
    "group": "User",
    "version": "1.0.0",
    "permission": [
      {
        "name": "User"
      }
    ],
    "header": {
      "fields": {
        "Header": [
          {
            "group": "Header",
            "type": "String",
            "optional": false,
            "field": "Authorization",
            "description": "<p>The JWT Token in format &quot;Bearer xxxx.yyyy.zzzz&quot;</p>"
          }
        ]
      }
    },
    "parameter": {
      "fields": {
        "Parameter": [
          {
            "group": "Parameter",
            "type": "String",
            "optional": false,
            "field": "id",
            "description": "<p>Team member's ID</p>"
          }
        ]
      }
    },
    "success": {
      "fields": {
        "Success 200": [
          {
            "group": "Success 200",
            "type": "Boolean",
            "optional": false,
            "field": "error",
            "description": "<p>Error flag</p>"
          },
          {
            "group": "Success 200",
            "type": "String",
            "optional": false,
            "field": "message",
            "description": "<p>Success message</p>"
          }
        ]
      },
      "examples": [
        {
          "title": "Success-Response:",
          "content": "{\n  \"error\": false,\n  \"message\": \"Team member deleted successfully\"\n}",
          "type": "json"
        }
      ]
    },
    "filename": "routes/rest/users.js",
    "groupTitle": "User"
  },
  {
    "type": "get",
    "url": "/teamMembers",
    "title": "2.0 Get all team members",
    "name": "getTeamMembers",
    "group": "User",
    "version": "1.0.0",
    "permission": [
      {
        "name": "User"
      }
    ],
    "header": {
      "fields": {
        "Header": [
          {
            "group": "Header",
            "type": "String",
            "optional": false,
            "field": "Authorization",
            "description": "<p>The JWT Token in format &quot;Bearer xxxx.yyyy.zzzz&quot;</p>"
          }
        ]
      }
    },
    "success": {
      "fields": {
        "Success 200": [
          {
            "group": "Success 200",
            "type": "Boolean",
            "optional": false,
            "field": "error",
            "description": "<p>Error flag</p>"
          },
          {
            "group": "Success 200",
            "type": "Array",
            "optional": false,
            "field": "teamMembers",
            "description": "<p>Array of team members</p>"
          }
        ]
      },
      "examples": [
        {
          "title": "Success-Response:",
          "content": "{\n  \"error\": false,\n  \"teamMembers\": [\n    {\n      \"id\": \"60d21b4667d0d8992e610c85\",\n      \"email\": \"<EMAIL>\",\n      \"name\": {\n        \"first\": \"John\",\n        \"last\": \"Doe\"\n      },\n      \"phone\": \"1234567890\",\n      \"address\": \"123 Main St, City, Country\",\n      \"imageUrl\": \"https://example.com/profile.jpg\"\n    }\n  ]\n}",
          "type": "json"
        }
      ]
    },
    "filename": "routes/rest/users.js",
    "groupTitle": "User"
  },
  {
    "type": "put",
    "url": "/teamMember/:id",
    "title": "3.0 Update a team member",
    "name": "updateTeamMember",
    "group": "User",
    "version": "1.0.0",
    "permission": [
      {
        "name": "User"
      }
    ],
    "header": {
      "fields": {
        "Header": [
          {
            "group": "Header",
            "type": "String",
            "optional": false,
            "field": "Authorization",
            "description": "<p>The JWT Token in format &quot;Bearer xxxx.yyyy.zzzz&quot;</p>"
          }
        ]
      }
    },
    "parameter": {
      "fields": {
        "Parameter": [
          {
            "group": "Parameter",
            "type": "String",
            "optional": false,
            "field": "id",
            "description": "<p>Team member's ID</p>"
          },
          {
            "group": "Parameter",
            "type": "String",
            "optional": true,
            "field": "name",
            "description": "<p>Full name of the team member (will be split into first and last name)</p>"
          },
          {
            "group": "Parameter",
            "type": "String",
            "optional": true,
            "field": "email",
            "description": "<p>Email of the team member</p>"
          },
          {
            "group": "Parameter",
            "type": "String",
            "optional": true,
            "field": "phone",
            "description": "<p>Phone number of the team member</p>"
          },
          {
            "group": "Parameter",
            "type": "String",
            "optional": true,
            "field": "address",
            "description": "<p>Address of the team member</p>"
          },
          {
            "group": "Parameter",
            "type": "String",
            "optional": true,
            "field": "imageUrl",
            "description": "<p>Profile image URL of the team member</p>"
          }
        ]
      }
    },
    "success": {
      "fields": {
        "Success 200": [
          {
            "group": "Success 200",
            "type": "Boolean",
            "optional": false,
            "field": "error",
            "description": "<p>Error flag</p>"
          },
          {
            "group": "Success 200",
            "type": "String",
            "optional": false,
            "field": "message",
            "description": "<p>Success message</p>"
          },
          {
            "group": "Success 200",
            "type": "Object",
            "optional": false,
            "field": "teamMember",
            "description": "<p>Updated team member</p>"
          }
        ]
      },
      "examples": [
        {
          "title": "Success-Response:",
          "content": "{\n  \"error\": false,\n  \"message\": \"Team member updated successfully\",\n  \"teamMember\": {\n    \"id\": \"60d21b4667d0d8992e610c85\",\n    \"email\": \"<EMAIL>\",\n    \"name\": {\n      \"first\": \"John\",\n      \"last\": \"Doe\"\n    },\n    \"phone\": \"1234567890\",\n    \"address\": \"123 Main St, City, Country\",\n    \"imageUrl\": \"https://example.com/profile.jpg\"\n  }\n}",
          "type": "json"
        }
      ]
    },
    "filename": "routes/rest/users.js",
    "groupTitle": "User"
  },
  {
    "type": "get",
    "url": "/user/:id",
    "title": "get user details",
    "name": "userDetails",
    "group": "User",
    "version": "1.0.0",
    "permission": [
      {
        "name": "User"
      }
    ],
    "header": {
      "fields": {
        "Header": [
          {
            "group": "Header",
            "type": "String",
            "optional": false,
            "field": "Authorization",
            "description": "<p>The JWT Token in format &quot;Bearer xxxx.yyyy.zzzz&quot;</p>"
          }
        ]
      }
    },
    "parameter": {
      "fields": {
        "Parameter": [
          {
            "group": "Parameter",
            "type": "String",
            "optional": false,
            "field": "id",
            "description": "<p>Users unique ID.</p>"
          }
        ]
      }
    },
    "success": {
      "fields": {
        "200": [
          {
            "group": "200",
            "type": "json",
            "optional": false,
            "field": "name",
            "description": "<p>description</p>"
          }
        ]
      },
      "examples": [
        {
          "title": "Success-Response:",
          "content": "{\n  \"error\" : false,\n  \"user\" : {\n    \"email\": \"<EMAIL>\",\n    \"phone\": \"00000000000\",\n    \"name\"  : {\n      \"first\":\"Jhon\",\n      \"last\" :\"Doe\"\n    }\n  }\n}",
          "type": "type"
        }
      ]
    },
    "filename": "routes/rest/users.js",
    "groupTitle": "User"
  },
  {
    "type": "post",
    "url": "/webhook/stripe",
    "title": "Stripe webhook handler",
    "name": "stripeWebhook",
    "group": "Webhook",
    "version": "1.0.0",
    "permission": [
      {
        "name": "None"
      }
    ],
    "description": "<p>Handles Stripe webhook events</p>",
    "filename": "routes/rest/webhook.js",
    "groupTitle": "Webhook"
  }
] });
