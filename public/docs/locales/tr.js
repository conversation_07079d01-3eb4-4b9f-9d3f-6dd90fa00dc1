define({
    tr: {
        'Allowed values:'             : '<PERSON>zin verilen de<PERSON>:',
        'Compare all with predecessor': '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ö<PERSON>kiler ile karşılaştır',
        'compare changes to:'         : 'değişiklikleri karşılaştır:',
        'compared to'                 : 'karşılaştır',
        'Default value:'              : 'Varsay<PERSON>lan de<PERSON>er:',
        'Description'                 : 'A<PERSON><PERSON>kla<PERSON>',
        'Field'                       : 'Alan',
        'General'                     : '<PERSON><PERSON>',
        'Generated with'              : 'Oluşturan',
        'Name'                        : 'İsim',
        'No response values.'         : 'Dönüş verisi yok.',
        'optional'                    : 'opsiyonel',
        'Parameter'                   : 'Parametre',
        'Permission:'                 : 'İzin:',
        'Response'                    : '<PERSON><PERSON><PERSON><PERSON><PERSON>',
        'Send'                        : 'Gö<PERSON>',
        'Send a Sample Request'       : '<PERSON>rnek istek gönder',
        'show up to version:'         : 'bu versiyona kadar göster:',
        'Size range:'                 : 'Boy<PERSON> aralığ<PERSON>:',
        'Type'                        : 'Tip',
        'url'                         : 'url'
    }
});
