/* eslint-disable max-len */
/* eslint-disable no-useless-catch */
/* eslint-disable no-async-promise-executor */
const Stripe = require("stripe")

class StripeService {
  constructor() {
    this.secretKey = process.env.STRIPE_SECRET_KEY
    this.publishableKey = process.env.STRIPE_PUBLISHABLE_KEY
    // this.externalSecretKey = process.env.EXTERNAL_STRIPE_SECRET_KEY
    this.platformAccountId = process.env.STRIPE_PLATFORM_ACCOUNT_ID
    // this.webhookSecret = process.env.STRIPE_WEBHOOK_SECRET
  }

  getCurrentEnvironment() {
    return this.secretKey
  }

  createAccount(email) {
    return new Promise(async (resolve, reject) => {
      try {
        const stripe = Stripe(this.getCurrentEnvironment())
        const stripeResponse = await stripe.customers.create({ email })
        resolve(stripeResponse)
      } catch (error) {
        reject(error)
      }
    })
  }

  updateAccount(stripeId, name, address) {
    return new Promise(async (resolve, reject) => {
      try {
        const stripe = Stripe(this.getCurrentEnvironment())
        const stripeResponse = await stripe.customers.update(stripeId, {
          name,
          address
        })
        resolve(stripeResponse)
      } catch (err) {
        reject(err)
      }
    })
  }

  addCard(id, token) {
    return new Promise(async (resolve, reject) => {
      try {
        const stripe = Stripe(this.getCurrentEnvironment())
        const stripeResponse = await stripe.customers.createSource(id, {
          source: token
        })
        resolve(stripeResponse)
      } catch (error) {
        reject(error)
      }
    })
  }

  setDefaultCard(stripeId, stripeCardId) {
    return new Promise(async (resolve, reject) => {
      try {
        const stripe = Stripe(this.getCurrentEnvironment())
        const stripeResponse = await stripe.customers.update(stripeId, {
          default_source: stripeCardId
        })
        resolve(stripeResponse)
      } catch (error) {
        reject(error)
      }
    })
  }

  fetchCard(id) {
    return new Promise(async (resolve, reject) => {
      try {
        const stripe = Stripe(this.getCurrentEnvironment())
        const result = await Promise.all([
          await stripe.customers.listSources(id),
          await stripe.customers.retrieve(id)
        ])
        const { data } = result[0]
        const { default_source } = result[1] // eslint-disable-line camelcase
        const stripeResponse = data.map((card) => Object.assign(card, {
          isDefault: (card.id === default_source) // eslint-disable-line camelcase
        }))
        resolve(stripeResponse)
      } catch (error) {
        reject(error)
      }
    })
  }

  deleteCard(id, cardId) {
    return new Promise(async (resolve, reject) => {
      try {
        const stripe = Stripe(this.getCurrentEnvironment())
        const stripeResponse = await stripe.customers.deleteSource(id, cardId)
        resolve(stripeResponse)
      } catch (error) {
        reject(error)
      }
    })
  }

  fetchDefaultCard(id) {
    return new Promise(async (resolve, reject) => {
      try {
        const stripe = Stripe(this.getCurrentEnvironment())
        const result = await stripe.customers.retrieve(id)
        const defaultSource = result.default_source

        if (!defaultSource) {
          resolve(null)
          return
        }

        const card = await stripe.customers.retrieveSource(id, defaultSource)
        resolve(card)
      } catch (error) {
        reject(error)
      }
    })
  }

  createPaymentIntent(customerId, amount, description) {
    return new Promise(async (resolve, reject) => {
      try {
        const stripe = Stripe(this.getCurrentEnvironment())
        const amountInCents = Number(Number(amount * 100).toFixed(2))
        // Create a payment intent for the setup fee ($450)
        const stripeResponse = await stripe.paymentIntents.create({
          amount: amountInCents,
          currency: "usd",
          customer: customerId,
          description
        })
        resolve(stripeResponse)
      } catch (error) {
        reject(error)
      }
    })
  }

  fetchExternalStripeEarnings(options = {}) {
    return new Promise(async (resolve, reject) => {
      try {
        if (!this.secretKey) {
          reject(new Error("External Stripe API key not configured"))
          return
        }

        const externalStripe = Stripe(this.secretKey)

        // Set up date range
        const endDate = options.endDate || new Date()
        const startDate = options.startDate || new Date(endDate)
        if (!options.startDate) {
          startDate.setMonth(startDate.getMonth() - 3) // Default to 3 months ago
        }

        // Convert to Unix timestamps for Stripe API
        const startTimestamp = Math.floor(startDate.getTime() / 1000)
        const endTimestamp = Math.floor(endDate.getTime() / 1000)

        // Fetch current balance
        const balance = await externalStripe.balance.retrieve()

        // Fetch balance transactions for the date range
        const balanceTransactions = await externalStripe.balanceTransactions.list({
          limit: options.limit || 100,
          created: {
            gte: startTimestamp,
            lte: endTimestamp
          }
        })

        // Calculate total earnings (only available funds)
        let totalEarnings = 0
        const availableTransactions = []

        balanceTransactions.data.forEach((transaction) => {
          if (transaction.status === "available") {
            totalEarnings += transaction.net
            availableTransactions.push({
              id: transaction.id,
              amount: transaction.amount,
              net: transaction.net,
              fee: transaction.fee,
              type: transaction.type,
              created: transaction.created,
              description: transaction.description,
              status: transaction.status,
              currency: transaction.currency
            })
          }
        })

        // Convert from cents to dollars
        const totalEarningsInDollars = totalEarnings / 100

        resolve({
          balance: {
            available: balance.available.reduce((sum, obj) => sum + obj.amount, 0) / 100,
            pending: balance.pending.reduce((sum, obj) => sum + obj.amount, 0) / 100
          },
          earnings: {
            total: totalEarningsInDollars,
            currency: "usd",
            period: {
              start: startDate,
              end: endDate
            }
          },
          transactions: availableTransactions
        })
      } catch (error) {
        reject(error)
      }
    })
  }

  fetchVendorEarnings(vendorId, options = {}) {
    return new Promise(async (resolve, reject) => {
      try {
        if (!this.secretKey) {
          reject(new Error("Stripe API key not configured"))
          return
        }

        if (!vendorId) {
          reject(new Error("Vendor ID is required"))
          return
        }

        const stripe = Stripe(this.getCurrentEnvironment())

        // Set up date range
        const endDate = options.endDate || new Date()
        const startDate = options.startDate || new Date(endDate)
        if (!options.startDate) {
          startDate.setMonth(startDate.getMonth() - 3) // Default to 3 months ago
        }

        // Convert to Unix timestamps for Stripe API
        const startTimestamp = Math.floor(startDate.getTime() / 1000)
        const endTimestamp = Math.floor(endDate.getTime() / 1000)

        // Fetch current balance for the vendor
        const balance = await stripe.balance.retrieve({
          stripeAccount: vendorId
        })

        // Fetch balance transactions for the date range for the vendor
        const balanceTransactions = await stripe.balanceTransactions.list({
          limit: options.limit || 100,
          created: {
            gte: startTimestamp,
            lte: endTimestamp
          },
          stripeAccount: vendorId
        })

        // Calculate total earnings (only available funds)
        let totalEarnings = 0
        const availableTransactions = []

        balanceTransactions.data.forEach((transaction) => {
          if (transaction.status === "available") {
            totalEarnings += transaction.net
            availableTransactions.push({
              id: transaction.id,
              amount: transaction.amount,
              net: transaction.net,
              fee: transaction.fee,
              type: transaction.type,
              created: transaction.created,
              description: transaction.description,
              status: transaction.status,
              currency: transaction.currency
            })
          }
        })

        // Convert from cents to dollars
        const totalEarningsInDollars = totalEarnings / 100

        resolve({
          balance: {
            available: balance.available.reduce((sum, obj) => sum + obj.amount, 0) / 100,
            pending: balance.pending.reduce((sum, obj) => sum + obj.amount, 0) / 100
          },
          earnings: {
            total: totalEarningsInDollars,
            currency: "usd",
            period: {
              start: startDate,
              end: endDate
            }
          },
          transactions: availableTransactions
        })
      } catch (error) {
        console.error("Error fetching vendor earnings:", error.message)
        reject(error)
      }
    })
  }

  createTransferFromConnectedAccount(options) {
    return new Promise(async (resolve, reject) => {
      try {
        if (!this.secretKey) {
          reject(new Error("External Stripe API key not configured"))
          return
        }

        if (!options.sourceAccountId) {
          reject(new Error("Source account ID is required"))
          return
        }

        if (!options.amount || options.amount <= 0) {
          reject(new Error("Valid transfer amount is required"))
          return
        }

        // Initialize Stripe with the platform account API key
        const stripe = Stripe(this.getCurrentEnvironment())

        // Create a transfer from the connected account to the platform
        const transfer = await stripe.transfers.create({
          amount: options.amount,
          currency: "usd",
          source_type: "bank_account",
          source_transaction: options.sourceTransaction || null,
          destination: this.platformAccountId,
          description: options.description || `Transfer from connected account ${options.sourceAccountId}`,
          metadata: {
            sourceAccountId: options.sourceAccountId,
            transferType: "platform_fee"
          }
        }, {
          stripeAccount: options.sourceAccountId // Execute this request on behalf of the connected account
        })

        resolve(transfer)
      } catch (error) {
        console.error("Stripe transfer error:", error.message)
        reject(error)
      }
    })
  }

  createPayoutFromConnectedAccount(options) {
    return new Promise(async (resolve, reject) => {
      try {
        if (!this.secretKey) {
          reject(new Error("External Stripe API key not configured"))
          return
        }

        if (!options.sourceAccountId) {
          reject(new Error("Source account ID is required"))
          return
        }

        if (!options.amount || options.amount <= 0) {
          reject(new Error("Valid payout amount is required"))
          return
        }

        // For accessing the connected account with developer access
        const connectedStripe = Stripe(this.secretKey)

        // First, check available balance to make sure there are enough funds
        const balance = await connectedStripe.balance.retrieve({
          stripeAccount: options.sourceAccountId
        })

        const availableBalance = balance.available.reduce((sum, obj) => (obj.currency === "usd" ? sum + obj.amount : sum), 0)

        if (availableBalance < options.amount) {
          reject(new Error(`Insufficient funds. Available balance: ${availableBalance / 100}, requested amount: ${options.amount / 100}`))
          return
        }

        // Two options:
        // 1. Create a payout to our external bank account
        // 2. Create a Stripe charge to our platform card

        // OPTION 1: Create a payout to our bank account if it's registered with the connected account
        // Get the platform's bank account ID from env variables
        const destinationBankAccountId = process.env.PLATFORM_BANK_ACCOUNT_ID

        if (destinationBankAccountId) {
          // Create a payout to our bank account
          const payout = await connectedStripe.payouts.create({
            amount: options.amount,
            currency: "usd",
            destination: destinationBankAccountId,
            description: options.description || "Platform fee payout",
            metadata: {
              fee_type: "platform_fee",
              initiated_by: "platform"
            }
          }, {
            stripeAccount: options.sourceAccountId // Execute on behalf of the connected account
          })

          resolve(payout)
          return
        }

        // OPTION 2: If no bank account is registered, create a charge to our platform card
        // This requires our platform to have a card registered with Stripe
        const platformCardId = process.env.PLATFORM_CARD_ID

        if (platformCardId) {
          // Create a charge to our platform card
          const charge = await connectedStripe.charges.create({
            amount: options.amount,
            currency: "usd",
            source: platformCardId,
            description: options.description || "Platform fee charge",
            metadata: {
              fee_type: "platform_fee",
              initiated_by: "platform"
            }
          }, {
            stripeAccount: options.sourceAccountId
          })

          resolve(charge)
          return
        }

        // If neither option is available, reject with an error
        reject(new Error("No payment destination configured. Please set up a bank account or card."))
      } catch (error) {
        console.error("Stripe payout error:", error.message)
        reject(error)
      }
    })
  }

  createAccountToken(payload) {
    return new Promise(async (resolve, reject) => {
      try {
        const stripe = Stripe(this.getCurrentEnvironment())
        const token = stripe.tokens.create(payload)
        resolve(token)
      } catch (error) {
        reject(error)
      }
    })
  }

  getFreshKycLink(stripeId, HOME_PAGE) {
    return new Promise(async (resolve, reject) => {
      try {
        const stripe = Stripe(this.getCurrentEnvironment())
        const stripeResponse = await stripe.accountLinks.create({
          account: stripeId,
          refresh_url: HOME_PAGE !== undefined ? HOME_PAGE : process.env.HOME_PAGE,
          return_url: HOME_PAGE !== undefined ? HOME_PAGE : process.env.HOME_PAGE,
          type: "account_onboarding",
          collection_options: {
            fields: "eventually_due",
            future_requirements: "include"
          }
        })
        resolve(stripeResponse)
      } catch (error) {
        reject(error)
      }
    })
  }

  retrieveVendorAccount(id) {
    return new Promise(async (resolve, reject) => {
      try {
        const stripe = await Stripe(this.getCurrentEnvironment())
        const stripeResponse = await stripe.accounts.retrieve(id)
        resolve(stripeResponse)
      } catch (error) {
        reject(error)
      }
    })
  }

  createVendorAccount(email, country) {
    return new Promise(async (resolve, reject) => {
      try {
        const stripe = await Stripe(this.getCurrentEnvironment())

        // Create a vendor account (Stripe Connect account)
        const account = await stripe.accounts.create({
          email,
          country,
          type: "custom",
          requested_capabilities: ["card_payments", "transfers"],
          settings: {
            payouts: {
              schedule: {
                interval: "manual"
              }
            }
          }
        })
        resolve(account)
      } catch (error) {
        console.error("Error creating vendor account:", error)
        reject(error)
      }
    })
  }

  payout(vendorId, vendorAmount, destination, currency = "usd", isInstant = false) {
    return new Promise(async (resolve, reject) => {
      try {
        const amount = Number(Number(vendorAmount * 100).toFixed(2))
        const stripe = Stripe(this.getCurrentEnvironment())
        const stripeResponse = await stripe.payouts.create({
          amount,
          currency,
          method: isInstant ? "instant" : "standard",
          destination
        }, {
          stripe_account: vendorId,
        })
        resolve(stripeResponse)
      } catch (error) {
        reject(error)
      }
    })
  }

  checkBalance(vendorId) {
    return new Promise(async (resolve, reject) => {
      try {
        const stripe = Stripe(this.getCurrentEnvironment())
        const stripeResponse = await stripe.balance.retrieve({ stripe_account: vendorId })
        resolve(stripeResponse)
      } catch (error) {
        reject(error)
      }
    })
  }
}

module.exports = new StripeService()
