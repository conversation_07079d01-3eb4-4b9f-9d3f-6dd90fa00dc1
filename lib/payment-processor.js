/* eslint-disable max-len */
const User = require("../models/user")
const Subscription = require("../models/subscription")
const stripeService = require("./stripe")
const Payment = require("../models/payment")
const mail = require("./mail")

/**
 * Transfers the platform fee from user's external Stripe account to the platform account
 * @param {string} vendorStripeId - The user's external Stripe account ID
 * @param {number} amount - The amount to transfer in cents
 * @param {string} description - Description for the transfer
 * @returns {Promise<object>} - The Stripe transfer object
 */
async function transferFeeToplatform(vendorStripeId, amount, description) {
  try {
    console.log(`Transferring ${amount} from account ${vendorStripeId} to platform`)

    // Amount must be in cents
    const amountInCents = Math.round(amount * 100)

    // Create a transfer from the connected account to the platform
    // Try the payout method first (for developer access)
    try {
      const payout = await stripeService.createPayoutFromConnectedAccount({
        amount: amountInCents,
        sourceAccountId: vendorStripeId,
        description: description || "Platform fee"
      })

      console.log(`Payout successful: ${payout.id}, amount: ${payout.amount / 100}`)
      return payout
    } catch (payoutError) {
      console.log(`Payout failed, attempting direct transfer: ${payoutError.message}`)

      // Fallback to transfer if payout fails
      const transfer = await stripeService.createTransferFromConnectedAccount({
        amount: amountInCents,
        sourceAccountId: vendorStripeId,
        description: description || "Platform fee"
      })

      console.log(`Transfer successful: ${transfer.id}, amount: ${transfer.amount / 100}`)
      return transfer
    }
  } catch (error) {
    console.error("Transfer failed:", error.message)
    throw error
  }
}

/**
 * Process successful payment
 * This function handles the transition from trial to paid subscription,
 * determines the appropriate pricing model based on revenue,
 * and schedules the next payment job
 *
 * @param {string} userId - The user ID
 * @param {string} subscriptionId - The subscription ID
 * @param {object} agenda - The agenda instance for scheduling
 */
async function processSubscriptionPayment(userId, subscriptionId, agenda) {
  console.log(`Processing subscription for subscription: ${subscriptionId} for user: ${userId}`)

  // Fetch the subscription and user
  const subscription = await Subscription.findById(subscriptionId)
  const user = await User.findById(userId)

  if (!subscription || !user) {
    console.error(`Subscription or user not found. Subscription: ${subscriptionId}, User: ${userId}`)
    return
  }

  try {
    // Check if user has a vendor Stripe account
    if (!user.vendorStripeId) {
      console.error(`User ${userId} does not have a vendor Stripe account. Cannot fetch earnings.`)
      return
    }

    // Use the stripeService to fetch earnings from the vendor's Stripe account
    const currentDate = new Date()
    const threeMonthsAgo = new Date(currentDate)
    threeMonthsAgo.setMonth(currentDate.getMonth() - 3)

    const stripeData = await stripeService.fetchVendorEarnings(user.vendorStripeId, {
      startDate: threeMonthsAgo,
      endDate: currentDate,
      limit: 100
    })

    console.log(`Fetched vendor Stripe data for ${user.vendorStripeId}:
      Available balance: $${stripeData.balance.available}
      Pending balance: $${stripeData.balance.pending}
      Total earnings (last 3 months): $${stripeData.earnings.total}
      Number of transactions: ${stripeData.transactions.length}
    `)

    // Calculate monthly average earnings (from the last 3 months)
    const monthlyAverageEarnings = stripeData.earnings.total / 3

    // Get threshold and fee values from environment variables
    const REVENUE_THRESHOLD = Number(process.env.REVENUE_THRESHOLD || 1500)
    const REVENUE_SHARE_PERCENTAGE = Number(process.env.REVENUE_SHARE_PERCENTAGE || 0.10)
    const PROCESSING_FEE_PERCENTAGE = Number(process.env.PROCESSING_FEE_PERCENTAGE || 0.05)
    const FLAT_FEE = Number(process.env.FLAT_FEE || 150)

    // Determine which pricing model to use based on monthly average earnings
    let subscriptionModel
    let platformFee
    let processingFee
    let totalFee
    let netPayout

    if (monthlyAverageEarnings > REVENUE_THRESHOLD) {
      // Revenue Share Model
      subscriptionModel = "revenue-share"
      platformFee = monthlyAverageEarnings * REVENUE_SHARE_PERCENTAGE
      processingFee = monthlyAverageEarnings * PROCESSING_FEE_PERCENTAGE
      totalFee = platformFee + processingFee
      netPayout = monthlyAverageEarnings - totalFee

      console.log(`User qualifies for Revenue Share Model:
        Average Monthly Earnings: $${monthlyAverageEarnings.toFixed(2)}
        Platform Fee (10%): $${platformFee.toFixed(2)}
        Processing Fee (5%): $${processingFee.toFixed(2)}
        Total Fees: $${totalFee.toFixed(2)}
        Net Payout: $${netPayout.toFixed(2)}
      `)
    } else {
      // Flat Fee Model
      subscriptionModel = "flat-fee"
      platformFee = FLAT_FEE
      processingFee = monthlyAverageEarnings * PROCESSING_FEE_PERCENTAGE
      totalFee = platformFee + processingFee
      netPayout = monthlyAverageEarnings - totalFee

      console.log(`User qualifies for Flat Fee Model:
        Average Monthly Earnings: $${monthlyAverageEarnings.toFixed(2)}
        Platform Fee (Flat): $${platformFee.toFixed(2)}
        Processing Fee (5%): $${processingFee.toFixed(2)}
        Total Fees: $${totalFee.toFixed(2)}
        Net Payout: $${netPayout.toFixed(2)}
      `)
    }

    // Calculate next billing date (1 month from now)
    const nextBillingDate = new Date(currentDate)
    nextBillingDate.setMonth(currentDate.getMonth() + 1)

    // Process the payment if the user has an external Stripe account
    let paymentStatus = "pending"
    let transferId = null

    if (user.vendorStripeId) {
      try {
        // Create a description for the transfer
        const description = `${subscriptionModel === "revenue-share" ? "Revenue share" : "Flat fee"} payment for ${user.email || userId}`

        // Transfer the fees from the connected account to the platform
        const transfer = await transferFeeToplatform(
          user.vendorStripeId,
          totalFee,
          description
        )

        // If we get here, the transfer was successful
        transferId = transfer.id
        paymentStatus = "completed"

        console.log(`Successfully transferred $${totalFee.toFixed(2)} from ${user.vendorStripeId} to platform account`)

        // Send payment notification email based on subscription model
        try {

            const firstName = user.name && user.name.first ? user.name.first : "User"

            if (subscriptionModel === "revenue-share") {
              // Send revenue share model email
              const billingSummaryLink = `${process.env.SITE_URL}/account/payments`

              await mail("revenue-share-model", {
                to: user.email,
                subject: "Platform Billing Update – Revenue Share Model Applied 💼",
                locals: {
                  userFirstName: firstName,
                  billingSummaryLink,
                  platformName: "Vewmee White Level"
                }
              })
              console.log(`Revenue share payment email sent to ${user.email}`)
            } else if (subscriptionModel === "flat-fee") {
              // Send flat fee model email
              const billingDetailsLink = `${process.env.SITE_URL}/account/payments`

              await mail("flat-fee", {
                to: user.email,
                subject: "You’ve Been Charged the Flat Platform Fee for This Month 💡",
                locals: {
                  userFirstName: firstName,
                  billingDetailsLink,
                  platformName: "Vewmee White Level"
                }
              })
              console.log(`Flat fee payment email sent to ${user.email}`)
          }
        } catch (emailError) {
          console.error(`Error sending ${subscriptionModel} payment email to ${user.email}:`, emailError.message)
          // Continue execution even if email fails
        }
      } catch (paymentError) {
        console.error(`Failed to process payment for user ${userId}:`, paymentError.message)
        paymentStatus = "failed"
      }
    } else {
      console.warn(`User ${userId} does not have an external Stripe account. Cannot process payment.`)
    }

    // Create a new payment document
    const paymentData = {
      _user: userId,
      amount: totalFee,
      platformFee,
      processingFee,
      totalDeducted: totalFee,
      netPayout,
      status: paymentStatus,
      stripePaymentId: transferId || null,
      // period: {
      //   start: stripeData.earnings.period.start,
      //   end: stripeData.earnings.period.end
      // },
      paymentFor: "subscription-renewal",
      metadata: stripeData.earnings,
      _subscription: subscriptionId,
      _platform: subscription._platform
    }

    await Payment.create(paymentData)

    // Update the subscription with earnings data and new subscription model
    await Subscription.findByIdAndUpdate(subscriptionId, {
      $set: {
        user: userId,
        status: paymentStatus === "completed" ? "active" : "past_due",
        setupFeePaid: subscription.setupFeePaid,
        // startDate: subscription.startDate || currentDate,
        // endDate: subscription.endDate || nextBillingDate,
        currentPeriodStart: currentDate,
        currentPeriodEnd: nextBillingDate,
        model: subscriptionModel,
        // stripeId: subscription.stripeId,
        defaultPaymentMethodId: subscription.defaultPaymentMethodId,
        trialEnd: subscription.trialEnd,
        canceledAt: subscription.canceledAt,
        earnings: stripeData.earnings.total
      }
    })

    console.log(`Updated subscription ${subscriptionId} with next billing date ${nextBillingDate.toISOString()}`)

    // Schedule the next payment job
    if (agenda) {
      await agenda.schedule(nextBillingDate, "process-successful-payment", {
        userId,
        subscriptionId
      })
      console.log(`Scheduled next payment check for ${nextBillingDate.toISOString()}`)
    }
  } catch (stripeError) {
    console.error("Error fetching data from vendor Stripe account:", stripeError)
  }
}

module.exports = {
  processSubscriptionPayment
}
