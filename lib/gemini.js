/* eslint-disable no-promise-executor-return */
/* eslint-disable prefer-destructuring */
/* eslint-disable no-restricted-syntax */
/* eslint-disable no-await-in-loop */
const { GoogleGenerativeAI } = require("@google/generative-ai")
const axios = require("axios")

/**
 * Generate logo prompt ideas using Google Gemini AI
 * @param {String} platformName - Name of the company
 * @returns {Promise<Array>} - Array of prompt ideas
 */
async function generatePrompts(platformName) {
  try {
    const ai = new GoogleGenerativeAI(process.env.GOOGLE_API_KEY)
    const model = ai.getGenerativeModel({ model: "gemini-1.5-flash" })

    const result = await model.generateContent(
      `Generate 3 unique logo ideas for a tech company called "${platformName}". Each idea should be visually descriptive, include colors, shapes, and the theme.`
    )

    const text = await result.response.text()

    return text
      .split("\n")
      .filter((line) => /^\d+[).\s]/.test(line))
      .map((line) => line.replace(/^\d+[).s]+/, "").trim())
      .slice(0, 3)
  } catch (error) {
    console.error("Error generating prompts:", error)
    throw new Error(`Failed to generate logo prompts: ${error.message}`)
  }
}

/**
 * Generate images from prompts using Replicate API
 * @param {Array} prompts - Array of text prompts
 * @returns {Promise<Array>} - Array of image URLs
 */
async function generateImagesFromPrompts(prompts) {
  try {
    const images = []

    for (const prompt of prompts) {
      const res = await axios.post(
        "https://api.replicate.com/v1/predictions",
        {
          version: "db21e45f3700c4a1c7c7bcd3c53e5fc7df47e3c0c9fc7c44c61e16c6e78b6b08", // Stable Diffusion
          input: { prompt },
        },
        {
          headers: {
            Authorization: `Token ${process.env.REPLICATE_API_TOKEN}`,
            "Content-Type": "application/json",
          },
        }
      )

      const { id } = res.data

      // Polling until image is ready
      let imageURL
      while (!imageURL) {
        const poll = await axios.get(`https://api.replicate.com/v1/predictions/${id}`, {
          headers: {
            Authorization: `Token ${process.env.REPLICATE_API_TOKEN}`,
          },
        })

        if (poll.data.status === "succeeded") {
          imageURL = poll.data.output[0]
        } else if (poll.data.status === "failed") {
          throw new Error("Image generation failed.")
        }

        await new Promise((r) => setTimeout(r, 1000))
      }

      images.push(imageURL)
    }

    return images
  } catch (error) {
    console.error("Error generating images:", error)
    throw new Error(`Failed to generate logo images: ${error.message}`)
  }
}

module.exports = {
  generatePrompts,
  generateImagesFromPrompts
}
