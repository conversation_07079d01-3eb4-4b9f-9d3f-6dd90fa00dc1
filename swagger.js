const swaggerJsdoc = require("swagger-jsdoc")
const swaggerUi = require("swagger-ui-express")

// Swagger definition
const swaggerOptions = {
  definition: {
    openapi: "3.0.0",
    info: {
      title: "VewMee API",
      version: "1.0.0",
      description: "API documentation for VewMee Service",
      contact: {
        name: "API Support",
        email: "<EMAIL>"
      }
    },
    servers: [
      {
        url: `/api/v${process.env.API_VERSION}`,
        description: "API Server"
      },
      {
        url: `/admin/v${process.env.API_VERSION}`,
        description: "Admin Server"
      }
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: "http",
          scheme: "bearer",
          bearerFormat: "JWT"
        }
      }
    },
    security: [
      {
        bearerAuth: []
      }
    ]
  },
  apis: ["./routes/**/*.js"], // Path to the API docs
}

// Initialize swagger-jsdoc
const swaggerSpec = swaggerJsdoc(swaggerOptions)

module.exports = {
  swaggerSpec,
  swaggerUi
}
