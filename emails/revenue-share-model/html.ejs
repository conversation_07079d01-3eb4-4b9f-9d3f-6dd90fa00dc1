<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8" />
  <title>Billing Cycle Notification</title>
</head>
<body style="margin: 0; padding: 0; background-color: #f0f2f5; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
  <table width="100%" cellpadding="0" cellspacing="0" style="max-width: 620px; margin: 40px auto; background-color: #ffffff; border-radius: 12px; box-shadow: 0 6px 18px rgba(0, 0, 0, 0.1); overflow: hidden;">
    
    <!-- Header Image (optional) -->
    <tr>
      <td>
        <img src="cid:billing-header" alt="Billing Notification" style="width: 100%; height: auto; object-fit: cover;" />
      </td>
    </tr>

    <!-- Email Content -->
    <tr>
      <td style="padding: 30px;">
        <h2 style="color: #2c3e50; font-weight: 600;">Hi <%= userFirstName %>,</h2>

        <p style="font-size: 16px; color: #555; line-height: 1.6;">
          Your <strong>3-month launch period</strong> has ended, and your platform has now entered the standard billing cycle.
        </p>

        <p style="font-size: 16px; color: #555; line-height: 1.6;">
          📊 <strong>This Month’s Billing:</strong> Your earnings qualify for our revenue share model — <strong>10% of your monthly income</strong>, plus a <strong>5% processing fee</strong>.
        </p>

        <p style="font-size: 16px; color: #555; line-height: 1.6;">
          There’s nothing you need to do — this will be automatically deducted from your Stripe balance at month-end.
        </p>

        <p style="font-size: 16px; color: #555; line-height: 1.6;">
          You can view a detailed breakdown anytime in your account.
        </p>

        <!-- CTA Button -->
        <div style="margin-top: 30px; text-align: center;">
          <a href="<%= billingSummaryLink %>" style="background-color: #3498db; color: white; text-decoration: none; padding: 12px 24px; font-size: 16px; border-radius: 8px; display: inline-block;">
            View My Billing Summary
          </a>
        </div>

        <!-- Footer -->
        <p style="font-size: 14px; color: #999; margin-top: 40px; text-align: center;">
          — The <%= platformName %> Team
        </p>
      </td>
    </tr>
  </table>
</body>
</html>
