<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8" />
  <title>Flat Fee Billing Notification</title>
</head>
<body style="margin: 0; padding: 0; background-color: #f0f2f5; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;">
  <table width="100%" cellpadding="0" cellspacing="0" style="max-width: 620px; margin: 40px auto; background-color: #ffffff; border-radius: 12px; box-shadow: 0 6px 18px rgba(0, 0, 0, 0.1); overflow: hidden;">
    
    <!-- Header Image (optional) -->
    <tr>
      <td>
        <img src="cid:billing-header" alt="Flat Fee Billing" style="width: 100%; height: auto; object-fit: cover;" />
      </td>
    </tr>

    <!-- Email Content -->
    <tr>
      <td style="padding: 30px;">
        <h2 style="color: #2c3e50; font-weight: 600;">Hi <%= userFirstName %>,</h2>

        <p style="font-size: 16px; color: #555; line-height: 1.6;">
          This month, your earnings were under <strong>$1,500</strong>, so you’ve been automatically placed on our flat monthly fee plan:
        </p>

        <p style="font-size: 16px; color: #555; line-height: 1.6;">
          💰 <strong>$165/month</strong> + <strong>5% processing fee</strong>
        </p>

        <p style="font-size: 16px; color: #555; line-height: 1.6;">
          This model ensures predictable costs for lower-earning periods and keeps your platform running without interruption.
        </p>

        <p style="font-size: 16px; color: #555; line-height: 1.6;">
          No action is needed — the amount will be deducted from your Stripe balance.
        </p>

        <!-- CTA Button -->
        <div style="margin-top: 30px; text-align: center;">
          <a href="<%= billingDetailsLink %>" style="background-color: #3498db; color: white; text-decoration: none; padding: 12px 24px; font-size: 16px; border-radius: 8px; display: inline-block;">
            View Billing Details
          </a>
        </div>

        <!-- Footer -->
        <p style="font-size: 14px; color: #999; margin-top: 40px; text-align: center;">
          — The <%= platformName %> Team
        </p>
      </td>
    </tr>
  </table>
</body>
</html>
