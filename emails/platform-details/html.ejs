<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Your Platform Links</title>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap');

    body {
      font-family: 'Poppins', Arial, sans-serif;
      line-height: 1.6;
      color: #333;
      margin: 0;
      padding: 0;
      background-color: #f5f7fa;
    }

    .container {
      max-width: 650px;
      margin: 0 auto;
      padding: 0;
      background-color: #ffffff;
      border-radius: 10px;
      overflow: hidden;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }

    .header {
      background: linear-gradient(135deg, #6a11cb 0%, #2575fc 100%);
      padding: 30px 20px;
      text-align: center;
      color: white;
    }

    .header h1 {
      margin: 0;
      font-size: 28px;
      font-weight: 600;
      letter-spacing: 0.5px;
      animation: fadeIn 1s ease-in-out;
    }

    .header p {
      margin: 10px 0 0;
      font-size: 16px;
      opacity: 0.9;
      animation: slideUp 1s ease-in-out;
    }

    .content {
      padding: 30px;
      background-color: #ffffff;
    }

    .greeting {
      font-size: 18px;
      margin-bottom: 20px;
      color: #333;
      animation: fadeIn 1.2s ease-in-out;
    }

    .message {
      margin-bottom: 25px;
      color: #555;
      line-height: 1.7;
      animation: fadeIn 1.4s ease-in-out;
    }

    .links {
      margin: 30px 0;
      display: flex;
      flex-direction: column;
      gap: 15px;
    }

    .link-item {
      padding: 15px;
      border-radius: 8px;
      background-color: #f8f9fa;
      border-left: 4px solid #2575fc;
      transition: transform 0.3s ease, box-shadow 0.3s ease;
      animation: slideIn 0.5s ease-in-out;
      animation-fill-mode: both;
    }

    .link-item:hover {
      transform: translateY(-3px);
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .link-item:nth-child(1) {
      animation-delay: 0.3s;
      border-left-color: #2575fc;
    }

    .link-item:nth-child(2) {
      animation-delay: 0.6s;
      border-left-color: #11cb79;
    }

    .link-item:nth-child(3) {
      animation-delay: 0.9s;
      border-left-color: #cb6a11;
    }

    .link-title {
      display: block;
      font-weight: 600;
      margin-bottom: 8px;
      color: #333;
    }

    .link-description {
      font-size: 14px;
      color: #666;
      margin-bottom: 12px;
    }

    .button {
      display: inline-block;
      background: linear-gradient(135deg, #2575fc 0%, #6a11cb 100%);
      color: white;
      text-decoration: none;
      padding: 10px 20px;
      border-radius: 50px;
      font-weight: 500;
      transition: all 0.3s ease;
      box-shadow: 0 4px 10px rgba(37, 117, 252, 0.2);
    }

    .button:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 15px rgba(37, 117, 252, 0.3);
    }

    .feedback-section {
      background-color: #f0f7ff;
      padding: 20px;
      border-radius: 8px;
      margin-top: 30px;
      border-left: 4px solid #2575fc;
      animation: fadeIn 1.6s ease-in-out;
    }

    .feedback-title {
      font-weight: 600;
      color: #2575fc;
      margin-bottom: 10px;
    }

    .footer {
      background-color: #f8f9fa;
      padding: 20px;
      text-align: center;
      font-size: 14px;
      color: #777;
      border-top: 1px solid #eee;
    }

    .social-links {
      margin-top: 15px;
      display: flex;
      justify-content: center;
      gap: 15px;
    }

    .social-icon {
      display: inline-block;
      width: 36px;
      height: 36px;
      background-color: #2575fc;
      border-radius: 50%;
      color: white;
      line-height: 36px;
      text-align: center;
      transition: transform 0.3s ease;
    }

    .social-icon:hover {
      transform: scale(1.1);
    }

    @keyframes fadeIn {
      from { opacity: 0; }
      to { opacity: 1; }
    }

    @keyframes slideUp {
      from { transform: translateY(20px); opacity: 0; }
      to { transform: translateY(0); opacity: 1; }
    }

    @keyframes slideIn {
      from { transform: translateX(-20px); opacity: 0; }
      to { transform: translateX(0); opacity: 1; }
    }

    @keyframes pulse {
      0% { transform: scale(1); }
      50% { transform: scale(1.05); }
      100% { transform: scale(1); }
    }

    .highlight {
      color: #2575fc;
      font-weight: 600;
    }

    .animated-pulse {
      animation: pulse 2s infinite;
    }
  </style>
</head>
<body>
  <div class="container">
    <div class="header">
      <h1>Your Platform Links</h1>
      <p>Access your digital presence with one click</p>
    </div>

    <div class="content">
      <div class="greeting">
        Hello<% if (creatorName) { %> <%= creatorName %><% } %>,
      </div>
      
      <div class="message">
        We're excited to share all your important platform links in one place!  
        Below you'll find quick access to everything you need—from your main website to your fan and creator dashboards.
      </div>
      

      <div class="links">
        <% if (website) { %>
        <div class="link-item">
          <span class="link-title">Your Website</span>
          <span class="link-description">The main hub for your audience to discover your content and offerings.</span>
          <a href="<%= website %>" class="button animated-pulse">Visit Website</a>
        </div>
        <% } %>

        <% if (fanAppLink) { %>
        <div class="link-item">
          <span class="link-title">Fan Application</span>
          <span class="link-description">Where your fans can engage with exclusive content and community features.</span>
          <a href="<%= fanAppLink %>" class="button">Open Fan App</a>
        </div>
        <% } %>

        <% if (creatorLink) { %>
<div class="link-item">
  <span class="link-title">Creator Dashboard</span>
  <span class="link-description">Your control center for managing content, analytics, and audience engagement.</span>
  <div>
    <a href="<%= creatorLink %>" class="button">Access Dashboard</a>
  </div>
</div>
<% } %>

      </div>

      <div class="feedback-section">
        <div class="feedback-title">Your Feedback Matters</div>
        <p>We're constantly working to improve your experience. Your feedback helps us make our platform better for you and all creators.</p>
      </div>
    </div>

    <div class="footer">
      <p>This is an automated message from the platform team.</p>
      <p>© <%= new Date().getFullYear() %> VewMee. All rights reserved.</p>
    </div>
  </div>
</body>
</html>
