{"env": {"es6": true, "node": true, "mocha": true}, "extends": "airbnb-base", "rules": {"func-names": ["error", "never"], "comma-dangle": ["error", "only-multiline"], "semi": ["warn", "never"], "quotes": ["warn", "double"], "max-len": ["warn", {"ignoreComments": true, "ignoreTrailingComments": true, "ignoreUrls": true, "ignoreStrings": true, "ignoreTemplateLiterals": true, "ignoreRegExpLiterals": true}], "no-console": 0, "no-unused-vars": 1, "prefer-const": 1, "no-var": 1, "eol-last": 1, "padded-blocks": 1, "import/newline-after-import": 0, "no-underscore-dangle": 0, "camelcase": 0}}