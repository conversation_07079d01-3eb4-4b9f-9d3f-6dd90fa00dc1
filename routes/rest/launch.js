const { User } = require("../../models")

module.exports = {
  /**
   * @swagger
   * /launch:
   *   get:
   *     summary: Get launch status
   *     tags: [Launch]
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: Launch status retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     launchStatus:
   *                       type: string
   *                       example: "review"
   *                       enum: [signup, subscription, domain, stripe, documents, review, launched]
   *                     websiteStatus:
   *                       type: string
   *                       example: "preview"
   *                       enum: [preview, active]
   *                     previewUrls:
   *                       type: object
   *                       properties:
   *                         website:
   *                           type: string
   *                           example: "/preview/60d21b4667d0d8992e610c85/website"
   *                         fanApp:
   *                           type: string
   *                           example: "/preview/60d21b4667d0d8992e610c85/fan-app"
   *                         creatorApp:
   *                           type: string
   *                           example: "/preview/60d21b4667d0d8992e610c85/creator-app"
   *                     liveUrls:
   *                       type: object
   *                       nullable: true
   *                       properties:
   *                         website:
   *                           type: string
   *                           example: "https://my-platform.example.com"
   *                         fanApp:
   *                           type: string
   *                           example: "https://fan.my-platform.example.com"
   *                         creatorApp:
   *                           type: string
   *                           example: "https://creator.my-platform.example.com"
   *       404:
   *         description: User not found
   *       500:
   *         description: Server error
   */
  async get(req, res) {
    try {
      const userId = req.user.id

      const user = await User.findOne({ _id: userId })
        .select("launchStatus websiteStatus platform")
        .lean()

      if (!user) {
        return res.status(404).json({
          error: true,
          reason: "User not found"
        })
      }

      // Generate preview URLs
      const previewUrls = {
        website: `/preview/${userId}/website`,
        fanApp: `/preview/${userId}/fan-app`,
        creatorApp: `/preview/${userId}/creator-app`
      }

      // Generate live URLs if launched
      let liveUrls = null
      if (user.launchStatus === "launched") {
        liveUrls = {
          website: `https://${user.platform.name.toLowerCase().replace(/\s+/g, "-")}.example.com`,
          fanApp: `https://fan.${user.platform.name.toLowerCase().replace(/\s+/g, "-")}.example.com`,
          creatorApp: `https://creator.${user.platform.name.toLowerCase().replace(/\s+/g, "-")}.example.com`
        }
      }

      return res.json({
        error: false,
        data: {
          launchStatus: user.launchStatus,
          websiteStatus: user.websiteStatus,
          previewUrls,
          liveUrls
        }
      })
    } catch (error) {
      return res.status(500).json({
        error: true,
        reason: error.message
      })
    }
  },

  /**
   * @swagger
   * /launch/review:
   *   post:
   *     summary: Review website before launch
   *     tags: [Launch]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: false
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               feedback:
   *                 type: string
   *                 description: Optional feedback
   *                 maxLength: 500
   *           example:
   *             feedback: "The website looks great! I'm ready to proceed with the launch."
   *     responses:
   *       200:
   *         description: Website marked as reviewed successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 message:
   *                   type: string
   *                   example: "Website marked as reviewed"
   *                 launchStatus:
   *                   type: string
   *                   example: "review"
   *       400:
   *         description: Bad request - feedback too long
   *       404:
   *         description: User not found
   *       500:
   *         description: Server error
   */
  async review(req, res) {
    try {
      const userId = req.user.id
      const { feedback } = req.body

      // Get user
      const user = await User.findOne({ _id: userId })

      if (!user) {
        return res.status(404).json({
          error: true,
          reason: "User not found"
        })
      }

      // Validate feedback length if provided
      if (feedback && feedback.length > 500) {
        return res.status(400).json({
          error: true,
          reason: "Feedback must be less than 500 characters"
        })
      }

      // Update launch status
      if (user.launchStatus === "documents") {
        user.launchStatus = "review"
      }

      await user.save()

      // In a real implementation, we would store the feedback
      // For now, we'll just acknowledge it

      return res.json({
        error: false,
        message: "Website marked as reviewed",
        launchStatus: user.launchStatus
      })
    } catch (error) {
      return res.status(500).json({
        error: true,
        reason: error.message
      })
    }
  },

  /**
   * @swagger
   * /launch/confirm:
   *   post:
   *     summary: Launch website
   *     tags: [Launch]
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: Website launched successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 message:
   *                   type: string
   *                   example: "Website launched successfully"
   *                 launchStatus:
   *                   type: string
   *                   example: "launched"
   *                 websiteStatus:
   *                   type: string
   *                   example: "active"
   *                 liveUrls:
   *                   type: object
   *                   properties:
   *                     website:
   *                       type: string
   *                       example: "https://my-platform.example.com"
   *                     fanApp:
   *                       type: string
   *                       example: "https://fan.my-platform.example.com"
   *                     creatorApp:
   *                       type: string
   *                       example: "https://creator.my-platform.example.com"
   *                 testAccount:
   *                   type: object
   *                   properties:
   *                     email:
   *                       type: string
   *                       example: "<EMAIL>"
   *                     password:
   *                       type: string
   *                       example: "Test123!"
   *       400:
   *         description: Bad request - all steps must be completed before launching
   *       404:
   *         description: User not found
   *       500:
   *         description: Server error
   */
  async confirm(req, res) {
    try {
      const userId = req.user.id

      // Get user
      const user = await User.findOne({ _id: userId })

      if (!user) {
        return res.status(404).json({
          error: true,
          reason: "User not found"
        })
      }

      // Check if all prerequisites are met
      if (user.launchStatus !== "review") {
        return res.status(400).json({
          error: true,
          reason: "All steps must be completed before launching"
        })
      }

      // Update launch status and website status
      user.launchStatus = "launched"
      user.websiteStatus = "active"

      await user.save()

      // Generate live URLs
      const liveUrls = {
        website: `https://${user.platform.name.toLowerCase().replace(/\s+/g, "-")}.example.com`,
        fanApp: `https://fan.${user.platform.name.toLowerCase().replace(/\s+/g, "-")}.example.com`,
        creatorApp: `https://creator.${user.platform.name.toLowerCase().replace(/\s+/g, "-")}.example.com`
      }

      // Generate test account credentials
      const testAccount = {
        email: `test@${user.platform.name.toLowerCase().replace(/\s+/g, "-")}.example.com`,
        password: "Test123!"
      }

      return res.json({
        error: false,
        message: "Website launched successfully",
        launchStatus: user.launchStatus,
        websiteStatus: user.websiteStatus,
        liveUrls,
        testAccount
      })
    } catch (error) {
      return res.status(500).json({
        error: true,
        reason: error.message
      })
    }
  }
}
