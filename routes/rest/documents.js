const mongoose = require("mongoose")
const { User, Document, Platform } = require("../../models")

const mail = require("../../lib/mail")

module.exports = {
  /**
   * @swagger
   * /documents/add:
   *   post:
   *     summary: Add a document URL
   *     tags: [Documents]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - type
   *               - documentUrl
   *               - platformId
   *             properties:
   *               type:
   *                 type: string
   *                 enum: [faqs, terms_and_conditions, privacy_policy, refund_policy, other]
   *                 description: Type of document
   *               documentUrl:
   *                 type: string
   *                 description: AWS URL to the document
   *               platformId:
   *                 type: string
   *                 description: ID of the platform this document belongs to
   *           example:
   *             type: "faqs"
   *             documentUrl: "https://aws-bucket.s3.amazonaws.com/documents/faqs.pdf"
   *             platformId: "60d21b4667d0d8992e610c85"
   *     responses:
   *       201:
   *         description: Document added successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     message:
   *                       type: string
   *                       example: "Document added successfully"
   *                     document:
   *                       type: object
   *                       properties:
   *                         _id:
   *                           type: string
   *                           example: "60d21b4667d0d8992e610c85"
   *                         type:
   *                           type: string
   *                           example: "faqs"
   *                         documentUrl:
   *                           type: string
   *                           example: "https://aws-bucket.s3.amazonaws.com/documents/faqs.pdf"
   *                         platformId:
   *                           type: string
   *                           example: "60d21b4667d0d8992e610c85"
   *       400:
   *         description: Bad request - missing required fields or invalid platform ID
   *       500:
   *         description: Server error
   */
  async addDocument(req, res) {
    try {
      const { type, documentUrl, platformId } = req.body

      // Validate required fields
      if (!type || !documentUrl || !platformId) {
        return res.status(400).json({
          error: true,
          reason: "Type, documentUrl, and platformId are required"
        })
      }

      // Validate platformId
      if (!mongoose.Types.ObjectId.isValid(platformId)) {
        return res.status(400).json({
          error: true,
          reason: "Invalid platform ID"
        })
      }

      // Check if document with this type already exists for this platform
      let document = await Document.findOne({ type, _platform: platformId })
      let message = "Document updated successfully"

      if (document) {
        // Update existing document
        document.documentUrl = documentUrl
        await document.save()
      } else {
        // Create new document
        document = await Document.create({
          type,
          documentUrl,
          _platform: platformId
        })
        message = "Document added successfully"
      }

      return res.status(201).json({
        error: false,
        data: {
          message,
          document: {
            _id: document._id,
            type: document.type,
            documentUrl: document.documentUrl,
            platformId: document._platform
          }
        }
      })
    } catch (error) {
      return res.status(500).json({
        error: true,
        reason: error.message
      })
    }
  },

  /**
   * @swagger
   * /documents/get/{id}:
   *   get:
   *     summary: Get all document URLs for a platform
   *     tags: [Documents]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Platform ID
   *     responses:
   *       200:
   *         description: Document URLs retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     documents:
   *                       type: array
   *                       items:
   *                         type: object
   *                         properties:
   *                           _id:
   *                             type: string
   *                             example: "60d21b4667d0d8992e610c85"
   *                           type:
   *                             type: string
   *                             example: "faqs"
   *                           documentUrl:
   *                             type: string
   *                             example: "https://aws-bucket.s3.amazonaws.com/documents/faqs.pdf"
   *       500:
   *         description: Server error
   */
  async getDocuments(req, res) {
    try {
      const { id } = req.params
      // Get all documents
      const documents = await Document.find({ _platform: id })
        .select("_id type documentUrl")
        .sort("type")

      return res.json({
        error: false,
        data: {
          documents
        }
      })
    } catch (error) {
      return res.status(500).json({
        error: true,
        reason: error.message
      })
    }
  },

  /**
   * @swagger
   * /documents/suggestion/add:
   *   post:
   *     summary: Add a suggestion to a document
   *     tags: [Documents]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - documentId
   *               - suggestion
   *             properties:
   *               documentId:
   *                 type: string
   *                 description: Document ID
   *               suggestion:
   *                 type: string
   *                 description: Suggestion content
   *                 maxLength: 1000
   *               platformId:
   *                 type: string
   *                 description: ID of the platform this document belongs to (optional)
   *           example:
   *             documentId: "60d21b4667d0d8992e610c85"
   *             suggestion: "Add more information about refunds in the FAQs section."
   *     responses:
   *       200:
   *         description: Suggestion added successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     message:
   *                       type: string
   *                       example: "Suggestion added successfully"
   *                     document:
   *                       type: object
   *                       properties:
   *                         _id:
   *                           type: string
   *                           example: "60d21b4667d0d8992e610c85"
   *                         suggestion:
   *                           type: string
   *                           example: "Add more information about refunds in the FAQs section."
   *                         suggestionStatus:
   *                           type: string
   *                           example: "active"
   *       400:
   *         description: Bad request - missing required fields or content too long
   *       404:
   *         description: Document not found
   *       500:
   *         description: Server error
   */
  async addSuggestion(req, res) {
    try {
      const userId = req.user.id
      const { documentId, suggestion } = req.body

      // Validate required fields
      if (!documentId || !suggestion) {
        return res.status(400).json({
          error: true,
          reason: "DocumentId and suggestion are required"
        })
      }

      // Validate ID
      if (!mongoose.Types.ObjectId.isValid(documentId)) {
        return res.status(400).json({
          error: true,
          reason: "Invalid document ID"
        })
      }

      // Validate suggestion length
      if (suggestion.length > 1000) {
        return res.status(400).json({
          error: true,
          reason: "Suggestion must be less than 1000 characters"
        })
      }

      // Get document
      const document = await Document.findById(documentId)

      if (!document) {
        return res.status(404).json({
          error: true,
          reason: "Document not found"
        })
      }

      // Update suggestion (now using content field)
      document.content = suggestion
      document.status = "active"
      document.addedBy = userId
      await document.save()

      return res.json({
        error: false,
        data: {
          message: "Suggestion added successfully",
          document: {
            _id: document._id,
            suggestion: document.content,
            suggestionStatus: document.status
          }
        }
      })
    } catch (error) {
      return res.status(500).json({
        error: true,
        reason: error.message
      })
    }
  },

  /**
   * @swagger
   * /documents/suggestion/get/{id}:
   *   get:
   *     summary: Get all document suggestions for a platform
   *     tags: [Documents]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Platform ID
   *     responses:
   *       200:
   *         description: Document suggestions retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     documents:
   *                       type: array
   *                       items:
   *                         type: object
   *                         properties:
   *                           _id:
   *                             type: string
   *                             example: "60d21b4667d0d8992e610c85"
   *                           type:
   *                             type: string
   *                             example: "faqs"
   *                           suggestion:
   *                             type: string
   *                             example: "Add more information about refunds"
   *                           suggestionStatus:
   *                             type: string
   *                             enum: [active, resolved]
   *                             example: "active"
   *       500:
   *         description: Server error
   */
  async getSuggestion(req, res) {
    try {
      const { id } = req.params
      // Get all documents with suggestions
      const documents = await Document.find({
        _platform: id,
        content: { $exists: true, $ne: "" },
        type: { $ne: "feedback" } // Exclude feedback documents
      })
        .select("_id type content status")
        .populate("addedBy", "name email")
        .sort("type")

      return res.json({
        error: false,
        data: {
          documents: documents.map((doc) => ({
            _id: doc._id,
            type: doc.type,
            suggestion: doc.content,
            suggestionStatus: doc.status,
            addedBy: doc.addedBy
          }))
        }
      })
    } catch (error) {
      return res.status(500).json({
        error: true,
        reason: error.message
      })
    }
  },

  /**
   * @swagger
   * /documents/suggestion/resolve:
   *   put:
   *     summary: Mark a document suggestion as resolved
   *     tags: [Documents]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - documentId
   *             properties:
   *               documentId:
   *                 type: string
   *                 description: Document ID
   *           example:
   *             documentId: "60d21b4667d0d8992e610c85"
   *     responses:
   *       200:
   *         description: Suggestion marked as resolved successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     message:
   *                       type: string
   *                       example: "Suggestion marked as resolved"
   *                     document:
   *                       type: object
   *                       properties:
   *                         _id:
   *                           type: string
   *                           example: "60d21b4667d0d8992e610c85"
   *                         suggestionStatus:
   *                           type: string
   *                           example: "resolved"
   *       400:
   *         description: Bad request - missing required fields
   *       404:
   *         description: Document not found
   *       500:
   *         description: Server error
   */
  async resolveSuggestion(req, res) {
    try {
      const { documentId } = req.body

      // Validate required fields
      if (!documentId) {
        return res.status(400).json({
          error: true,
          reason: "DocumentId is required"
        })
      }

      // Validate ID
      if (!mongoose.Types.ObjectId.isValid(documentId)) {
        return res.status(400).json({
          error: true,
          reason: "Invalid document ID"
        })
      }

      // Get document
      const document = await Document.findById(documentId)

      if (!document) {
        return res.status(404).json({
          error: true,
          reason: "Document not found"
        })
      }

      // Check if document has content
      if (!document.content || document.content.trim().length === 0) {
        return res.status(400).json({
          error: true,
          reason: "Document has no content to resolve"
        })
      }

      // Update status
      document.status = "resolved"
      await document.save()

      return res.json({
        error: false,
        data: {
          message: "Content marked as resolved",
          document: {
            _id: document._id,
            status: document.status
          }
        }
      })
    } catch (error) {
      return res.status(500).json({
        error: true,
        reason: error.message
      })
    }
  },

  /**
   * @swagger
   * /documents/feedback/add:
   *   post:
   *     summary: Add feedback
   *     tags: [Documents]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - content
   *               - platformId
   *             properties:
   *               content:
   *                 type: string
   *                 description: Feedback content
   *                 maxLength: 1000
   *               platformId:
   *                 type: string
   *                 description: ID of the platform this feedback belongs to
   *           example:
   *             content: "I really like the platform, but I think the navigation could be improved."
   *             platformId: "60d21b4667d0d8992e610c85"
   *     responses:
   *       201:
   *         description: Feedback added successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     message:
   *                       type: string
   *                       example: "Feedback added successfully"
   *                     document:
   *                       type: object
   *                       properties:
   *                         _id:
   *                           type: string
   *                           example: "60d21b4667d0d8992e610c85"
   *       400:
   *         description: Bad request - missing required fields or content too long
   *       500:
   *         description: Server error
   */
  async addFeedback(req, res) {
    try {
      const userId = req.user.id
      const { content, platformId } = req.body

      // Validate required fields
      if (!content) {
        return res.status(400).json({
          error: true,
          reason: "Content is required"
        })
      }

      // Validate content length
      if (content.length > 1000) {
        return res.status(400).json({
          error: true,
          reason: "Feedback must be less than 1000 characters"
        })
      }

      // Create new feedback document
      const document = await Document.create({
        type: "feedback",
        documentUrl: "N/A", // Not applicable for feedback
        content,
        status: "active",
        addedBy: userId,
        _platform: platformId
      })

      return res.status(201).json({
        error: false,
        data: {
          message: "Feedback added successfully",
          document: {
            _id: document._id
          }
        }
      })
    } catch (error) {
      console.log("error", error)
      return res.status(500).json({
        error: true,
        reason: error.message
      })
    }
  },

  /**
   * @swagger
   * /documents/feedback/get/{id}:
   *   get:
   *     summary: Get all feedback for a platform
   *     tags: [Documents]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Platform ID
   *     responses:
   *       200:
   *         description: Feedback retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     feedback:
   *                       type: array
   *                       items:
   *                         type: object
   *                         properties:
   *                           _id:
   *                             type: string
   *                             example: "60d21b4667d0d8992e610c85"
   *                           content:
   *                             type: string
   *                             example: "I really like the platform, but I think the navigation could be improved."
   *                           status:
   *                             type: string
   *                             enum: [active, resolved]
   *                             example: "active"
   *                           addedBy:
   *                             type: object
   *                             properties:
   *                               _id:
   *                                 type: string
   *                                 example: "60d21b4667d0d8992e610c85"
   *                               name:
   *                                 type: object
   *                                 properties:
   *                                   first:
   *                                     type: string
   *                                     example: "John"
   *                                   last:
   *                                     type: string
   *                                     example: "Doe"
   *                               email:
   *                                 type: string
   *                                 example: "<EMAIL>"
   *       500:
   *         description: Server error
   */
  async getFeedback(req, res) {
    try {
      const { id } = req.params
      // Get all feedback documents
      const feedback = await Document.find({ type: "feedback", _platform: id })
        .select("_id content status createdAt")
        .populate("addedBy", "name email")
        .sort("-createdAt")

      return res.json({
        error: false,
        data: {
          feedback
        }
      })
    } catch (error) {
      return res.status(500).json({
        error: true,
        reason: error.message
      })
    }
  },

  /**
   * @swagger
   * /documents/feedback/resolve/{id}:
   *   put:
   *     summary: Mark feedback as resolved
   *     tags: [Documents]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Feedback document ID
   *     responses:
   *       200:
   *         description: Feedback marked as resolved successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     message:
   *                       type: string
   *                       example: "Feedback marked as resolved"
   *       400:
   *         description: Bad request - invalid ID
   *       404:
   *         description: Feedback not found
   *       500:
   *         description: Server error
   */
  async resolveFeedback(req, res) {
    try {
      const { id } = req.params

      // Validate ID
      if (!mongoose.Types.ObjectId.isValid(id)) {
        return res.status(400).json({
          error: true,
          reason: "Invalid feedback ID"
        })
      }

      // Get feedback document
      const feedback = await Document.findOne({ _id: id, type: "feedback" })

      if (!feedback) {
        return res.status(404).json({
          error: true,
          reason: "Feedback not found"
        })
      }

      // Update status
      feedback.status = "resolved"
      await feedback.save()

      return res.json({
        error: false,
        data: {
          message: "Feedback marked as resolved"
        }
      })
    } catch (error) {
      return res.status(500).json({
        error: true,
        reason: error.message
      })
    }
  },

  /**
   * @swagger
   * /documents/suggestions/addbatch:
   *   post:
   *     summary: Add multiple document suggestions at once
   *     tags: [Documents]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - suggestions
   *             properties:
   *               suggestions:
   *                 type: array
   *                 description: Array of document suggestions
   *                 items:
   *                   type: object
   *                   required:
   *                     - documentId
   *                     - content
   *                   properties:
   *                     documentId:
   *                       type: string
   *                       description: Document ID
   *                     content:
   *                       type: string
   *                       description: Suggestion content
   *                       maxLength: 10000
   *           example:
   *             suggestions: [
   *               {
   *                 documentId: "60d21b4667d0d8992e610c85",
   *                 content: "Add more information about refunds in the FAQs section."
   *               },
   *               {
   *                 documentId: "60d21b4667d0d8992e610c86",
   *                 content: "Update the privacy policy to include GDPR compliance information."
   *               }
   *             ]
   *     responses:
   *       200:
   *         description: Suggestions added successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     message:
   *                       type: string
   *                       example: "Suggestions added successfully"
   *                     results:
   *                       type: array
   *                       items:
   *                         type: object
   *                         properties:
   *                           documentId:
   *                             type: string
   *                             example: "60d21b4667d0d8992e610c85"
   *                           error:
   *                             type: boolean
   *                             example: false
   *                           message:
   *                             type: string
   *                             example: "Suggestion added successfully"
   *       400:
   *         description: Bad request - missing required fields or invalid data
   *       500:
   *         description: Server error
   */
  async addBatchSuggestions(req, res) {
    try {
      const userId = req.user.id
      const { suggestions } = req.body

      // Validate required fields
      if (!suggestions
          || !Array.isArray(suggestions)
          || suggestions.length === 0) {
        return res.status(400).json({
          error: true,
          reason: "Suggestions array is required and must not be empty"
        })
      }

      // Process each suggestion
      const results = await Promise.all(
        suggestions.map(async (item) => {
          try {
            const { documentId, content } = item

            // Validate required fields
            if (!documentId || !content) {
              return {
                documentId: documentId || "unknown",
                error: true,
                message: "DocumentId and content are required"
              }
            }

            // Validate ID
            if (!mongoose.Types.ObjectId.isValid(documentId)) {
              return {
                documentId,
                error: true,
                message: "Invalid document ID"
              }
            }

            // Validate content length
            if (content.length > 10000) {
              return {
                documentId,
                error: true,
                message: "Suggestion must be less than 10000 characters"
              }
            }

            // Get document
            const document = await Document.findById(documentId)

            if (!document) {
              return {
                documentId,
                error: true,
                message: "Document not found"
              }
            }

            // Update suggestion (using content field)
            document.content = content
            document.status = "active"
            document.addedBy = userId
            await document.save()

            return {
              documentId,
              error: false,
              message: "Suggestion added successfully"
            }
          } catch (error) {
            return {
              documentId: item.documentId || "unknown",
              error: true,
              message: error.message
            }
          }
        })
      )

      // Check if any suggestions were successfully added
      const successCount = results.filter((result) => result.success).length

      return res.json({
        error: false,
        data: {
          message: `${successCount} of ${suggestions.length} suggestions added successfully`,
          results
        }
      })
    } catch (error) {
      return res.status(500).json({
        error: true,
        reason: error.message
      })
    }
  }
}
