/* eslint-disable no-plusplus */
const express = require("express")
const mongoose = require("mongoose")
const router = express.Router()
const { User, Platform } = require("../../models")

// POST /migration/platform-details
router.post("/platform-details", async (req, res) => {
  try {
    // Find all users who do not have a platform reference yet
    const users = await User.find({
      $or: [
        { platforms: { $exists: false } },
        { platforms: { $size: 0 } }
      ]
    })

    let migrated = 0
    await Promise.all(users.map(async (user) => {
      // Only migrate if user has platform details (adjust as needed)
      // Example: if user has a name or other platform fields
      if (!user.name || (!user.name.first && !user.name.last)) return

      // Make sure the user has userType set to Owner
      await User.updateOne({ _id: user._id }, { userType: "Owner" })

      // Create a new Platform document
      const platform = new Platform({
        primaryUser: user._id,
        teamMembers: [user._id], // Add the owner as a team member
        name: user.name && user.name.first ? user.name.first : undefined,
        // Add other fields here if they exist in User
        // colorPalette: user.colorPalette,
        // logo: user.logo,
        // ...
      })
      await platform.save()

      // Update user to reference the new platform
      const updatedUser = await User.findById(user._id)
      updatedUser.platforms = [platform._id]
      await updatedUser.save()
      migrated += 1
    }))

    res.json({ error: false, message: `Migrated ${migrated} users.` })
  } catch (error) {
    res.status(500).json({ error: true, reason: error.message })
  }
})

router.post("/users", async (req, res) => {
  try {
    // Fetch all users from the older schema
    const users = await User.find({})

    let migratedOwners = 0
    let migratedTeamMembers = 0

    await Promise.all(
      users.map(async (user) => {
        // Determine the user type based on the older schema
        if (user.isOwner) {
          // Migrate to the Owner discriminator
          await User.deleteOne({ _id: user._id }) // Remove the old user document
          const Owner = User.discriminator("Owner", new mongoose.Schema({}))
          await Owner.create({
            _id: user._id,
            username: user.username,
            password: user.password,
            name: user.name,
            email: user.email,
            accountType: user.accountType,
            socialId: user.socialId,
            phone: user.phone,
            address: user.address,
            imageUrl: user.imageUrl,
            gender: user.gender,
            isActive: user.isActive,
            forgotpassword: user.forgotpassword,
            platforms: user.platforms,
            isEmailVerified: user.isEmailVerified,
            stripeId: user.stripeId,
            cards: user.cards,
            stripeAccess: user.stripeAccess,
            vendorStripeId: user.vendorStripeId
          })
          migratedOwners++
        } else {
          // Migrate to the TeamMember discriminator
          await User.deleteOne({ _id: user._id }) // Remove the old user document
          const TeamMember = User.discriminator("TeamMember", new mongoose.Schema({}))
          await TeamMember.create({
            _id: user._id,
            username: user.username,
            password: user.password,
            name: user.name,
            email: user.email,
            accountType: user.accountType,
            socialId: user.socialId,
            phone: user.phone,
            address: user.address,
            imageUrl: user.imageUrl,
            gender: user.gender,
            isActive: user.isActive,
            forgotpassword: user.forgotpassword,
            teamMembers: user.teamMembers,
            isEmailVerified: user.isEmailVerified,
            _owner: user._owner
          })
          migratedTeamMembers++
        }
      })
    )

    res.json({
      error: false,
      message: `Migration completed. Migrated ${migratedOwners} owners and ${migratedTeamMembers} team members.`,
    })
  } catch (error) {
    console.error("Migration error:", error)
    res.status(500).json({ error: true, reason: error.message })
  }
})

module.exports = router
