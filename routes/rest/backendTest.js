/* eslint-disable max-len */
const stripeVendorService = require("../../lib/stripe")
const { User } = require("../../models")
async function makeVendor(req, res) {
  try {
    const user = await User.findOne({ _id: req.user._id })
    console.log("user", user)
    const vendorAccount = await stripeVendorService.createVendorAccount(user.email, process.env.BUSINESS_COUNTRY)
    console.log("vendorAccount", vendorAccount)
    user.vendorStripeId = vendorAccount.id // Save the vendor account ID
    await user.save()
    res.json({ error: false })
  } catch (error) {
    console.log("error in makeVendor", error)
    res.status(500).json({ error: true, reason: error.message })
  }
}
module.exports = {
  makeVendor
}
