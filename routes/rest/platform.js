/* eslint-disable max-len */
const { User, Comment, Platform } = require("../../models")
const { generateImagesFromPrompts } = require("../../lib/gemini")
const mail = require("../../lib/mail")

module.exports = {

  /**
   * @swagger
   * /platform/add:
   *   post:
   *     summary: Add a new platform for a creator
   *     description: Creates a new platform for the authenticated user (must be an Owner)
   *     tags: [Platform]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               name:
   *                 type: string
   *                 description: Platform name
   *                 minLength: 2
   *                 maxLength: 50
   *               colorPalette:
   *                 type: string
   *                 description: Color palette for the platform
   *               logo:
   *                 type: string
   *                 description: URL to the logo image
   *               socialProfiles:
   *                 type: object
   *                 description: Social media profiles
   *                 properties:
   *                   facebook:
   *                     type: string
   *                   twitter:
   *                     type: string
   *                   instagram:
   *                     type: string
   *                   other:
   *                     type: string
   *               ideaDescription:
   *                 type: string
   *                 description: Description of the platform idea
   *                 maxLength: 300
   *               existingWebsite:
   *                 type: string
   *                 description: URL of existing website, if any
   *               specialRequirements:
   *                 type: string
   *                 description: Special requirements for the platform
   *                 maxLength: 300
   *           examples:
   *             basic_platform:
   *               summary: Basic platform with minimal information
   *               value:
   *                 name: "My New Platform"
   *             full_platform:
   *               summary: Complete platform with all details
   *               value:
   *                 name: "Creative Studio"
   *                 colorPalette: "blue"
   *                 logo: "https://example.com/logo.png"
   *                 socialProfiles:
   *                   facebook: "creativestudio"
   *                   twitter: "creativestudio"
   *                   instagram: "creativestudio"
   *                 ideaDescription: "A platform for creative professionals to showcase their work"
   *                 existingWebsite: "https://creativestudio.com"
   *                 specialRequirements: "Need portfolio functionality and client management"
   *     responses:
   *       201:
   *         description: Platform created successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     message:
   *                       type: string
   *                       example: "Platform created successfully"
   *                     platform:
   *                       type: object
   *                       properties:
   *                         _id:
   *                           type: string
   *                           example: "60d21b4667d0d8992e610c85"
   *                         name:
   *                           type: string
   *                           example: "Creative Studio"
   *                         primaryUser:
   *                           type: string
   *                           example: "60d21b4667d0d8992e610c84"
   *                         teamMembers:
   *                           type: array
   *                           items:
   *                             type: string
   *                           example: ["60d21b4667d0d8992e610c84"]
   *                         colorPalette:
   *                           type: string
   *                           example: "blue"
   *                         logo:
   *                           type: string
   *                           example: "https://example.com/logo.png"
   *                         socialProfiles:
   *                           type: object
   *                           example: { "facebook": "creativestudio", "twitter": "creativestudio", "instagram": "creativestudio" }
   *                         ideaDescription:
   *                           type: string
   *                           example: "A platform for creative professionals to showcase their work"
   *                         existingWebsite:
   *                           type: string
   *                           example: "https://creativestudio.com"
   *                         specialRequirements:
   *                           type: string
   *                           example: "Need portfolio functionality and client management"
   *                         launchStatus:
   *                           type: number
   *                           example: 1
   *                         websiteStatus:
   *                           type: string
   *                           example: "inactive"
   *             examples:
   *               success_response:
   *                 summary: Successful platform creation
   *                 value:
   *                   error: false
   *                   data:
   *                     message: "Platform created successfully"
   *                     platform:
   *                       _id: "60d21b4667d0d8992e610c85"
   *                       name: "Creative Studio"
   *                       primaryUser: "60d21b4667d0d8992e610c84"
   *                       teamMembers: ["60d21b4667d0d8992e610c84"]
   *                       colorPalette: "blue"
   *                       logo: "https://example.com/logo.png"
   *                       socialProfiles:
   *                         facebook: "creativestudio"
   *                         twitter: "creativestudio"
   *                         instagram: "creativestudio"
   *                       ideaDescription: "A platform for creative professionals to showcase their work"
   *                       existingWebsite: "https://creativestudio.com"
   *                       specialRequirements: "Need portfolio functionality and client management"
   *                       launchStatus: 1
   *                       websiteStatus: "inactive"
   *                       createdAt: "2023-01-01T00:00:00.000Z"
   *                       updatedAt: "2023-01-01T00:00:00.000Z"
   *       400:
   *         description: Bad request - validation error
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 reason:
   *                   type: string
   *                   example: "Platform name must be between 2-50 characters"
   *       403:
   *         description: Forbidden - user is not an owner
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 reason:
   *                   type: string
   *                   example: "Only owners can create platforms"
   *       404:
   *         description: User not found
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 reason:
   *                   type: string
   *                   example: "User not found"
   *       500:
   *         description: Server error
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 reason:
   *                   type: string
   *                   example: "Internal server error"
   */
  async addPlatform(req, res) {
    try {
      const userId = req.user.id
      const {
        name,
        colorPalette,
        logo,
        socialProfiles,
        ideaDescription,
        existingWebsite,
        specialRequirements
      } = req.body

      // Validate platform name if provided
      if (name && (name.length < 2 || name.length > 50)) {
        return res.status(400).json({
          error: true,
          reason: "Platform name must be between 2-50 characters"
        })
      }

      // Validate idea description if provided
      if (ideaDescription && ideaDescription.length > 300) {
        return res.status(400).json({
          error: true,
          reason: "Idea description must be less than 300 characters"
        })
      }

      // Validate special requirements if provided
      if (specialRequirements && specialRequirements.length > 300) {
        return res.status(400).json({
          error: true,
          reason: "Special requirements must be less than 300 characters"
        })
      }

      // Get the user
      const user = await User.findOne({ _id: userId })

      if (!user) {
        return res.status(404).json({
          error: true,
          reason: "User not found"
        })
      }

      // Check if user is an owner
      if (user.userType !== "Owner") {
        return res.status(403).json({
          error: true,
          reason: "Only owners can create platforms"
        })
      }

      // Create new platform for this user
      const platform = new Platform({
        primaryUser: userId,
        // teamMembers: [userId], // Add the owner as a team member
        name: name || "Unnamed Platform",
        colorPalette,
        logo,
        socialProfiles,
        ideaDescription,
        existingWebsite,
        specialRequirements,
        launchStatus: 1, // Initial launch status
        websiteStatus: "inactive" // Initial website status
      })

      // Save the platform
      await platform.save()

      // Update user with reference to the new platform
      if (!user.platforms) {
        user.platforms = []
      }
      user.platforms.push(platform._id)
      await user.save()

      return res.status(201).json({
        error: false,
        data: {
          message: "Platform created successfully",
          platform
        }
      })
    } catch (error) {
      console.error("Error creating platform:", error)
      return res.status(500).json({
        error: true,
        reason: error.message
      })
    }
  },

  /**
   * @swagger
   * /platform/list:
   *   get:
   *     summary: Get list of platforms with details
   *     tags: [Platform]
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: List of platforms retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     platforms:
   *                       type: array
   *                       items:
   *                         type: object
   *                         properties:
   *                           id:
   *                             type: string
   *                             example: "60d21b4667d0d8992e610c85"
   *                           name:
   *                             type: string
   *                             example: "My Platform"
   *                           colorPalette:
   *                             type: string
   *                             example: "blue"
   *                           logo:
   *                             type: string
   *                             example: "https://example.com/logo.png"
   *                           launchStatus:
   *                             type: number
   *                             example: 2
   *                           websiteStatus:
   *                             type: string
   *                             example: "active"
   *                           isOwner:
   *                             type: boolean
   *                             description: Indicates if the current user is the owner of this platform
   *       500:
   *         description: Server error
   */
  async list(req, res) {
    try {
      const userId = req.user.id

      // Find platforms where user is either the primary user or a team member
      const platforms = await Platform.find({
        primaryUser: userId
      }).lean()

      // Format the response
      const formattedPlatforms = platforms.map((platform) => ({
        id: platform._id,
        name: platform.name || "Unnamed Platform",
        colorPalette: platform.colorPalette,
        logo: platform.logo,
        launchStatus: platform.launchStatus,
        websiteStatus: platform.websiteStatus,
        website: platform.website,
        fanAppLink: platform.fanAppLink,
        creatorLink: platform.creatorLink,
        isOwner: platform.primaryUser.toString() === userId // true if user is the primary user (owner)
      }))

      return res.status(200).json({
        error: false,
        data: {
          platforms: formattedPlatforms
        }
      })
    } catch (error) {
      return res.status(500).json({
        error: true,
        reason: error.message
      })
    }
  },

  /**
   * @swagger
   * /platform/{id}:
   *   put:
   *     summary: Update platform details
   *     tags: [Platform]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Platform ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               name:
   *                 type: string
   *                 description: Platform name (same as platformName from signup)
   *               colorPalette:
   *                 type: string
   *                 description: Color palette
   *               logo:
   *                 type: string
   *                 description: Logo URL
   *               socialProfiles:
   *                 type: object
   *                 description: Social profiles
   *               ideaDescription:
   *                 type: string
   *                 description: Idea description
   *               existingWebsite:
   *                 type: string
   *                 description: Existing website
   *               specialRequirements:
   *                 type: string
   *                 description: Special requirements
   *               launchStatus:
   *                 type: number
   *                 description: Platform launch status
   *           example:
   *             name: "My Platform"
   *             colorPalette: "blue"
   *             logo: "https://example.com/logo.png"
   *             socialProfiles: { "twitter": "myhandle", "instagram": "myhandle" }
   *             ideaDescription: "A platform for connecting creators with fans"
   *             existingWebsite: "https://myexistingsite.com"
   *             specialRequirements: "Need mobile-friendly design"
   *             launchStatus: 2
   *     responses:
   *       200:
   *         description: Platform details updated successfully. A setup-started email is sent to the user.
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     platform:
   *                       type: object
   *                       properties:
   *                         name:
   *                           type: string
   *                           example: "My Platform"
   *                         colorPalette:
   *                           type: string
   *                           example: "blue"
   *                         logo:
   *                           type: string
   *                           example: "https://example.com/logo.png"
   *                         socialProfiles:
   *                           type: object
   *                           example: { "twitter": "myhandle", "instagram": "myhandle" }
   *                         ideaDescription:
   *                           type: string
   *                           example: "A platform for connecting creators with fans"
   *                         existingWebsite:
   *                           type: string
   *                           example: "https://myexistingsite.com"
   *                         specialRequirements:
   *                           type: string
   *                           example: "Need mobile-friendly design"
   *                         launchStatus:
   *                           type: number
   *                           example: 2
   *       400:
   *         description: Bad request - validation error
   *       404:
   *         description: User not found
   *       500:
   *         description: Server error
   */
  async update(req, res) {
    try {
      const userId = req.user.id
      const { id } = req.params
      const {
        name,
        colorPalette,
        logo,
        socialProfiles,
        ideaDescription,
        existingWebsite,
        specialRequirements,
        launchStatus
      } = req.body

      // Validate platform name
      if (name && (name.length < 2 || name.length > 50)) {
        return res.status(400).json({
          error: true,
          reason: "Platform name must be between 2-50 characters"
        })
      }

      // Validate idea description
      if (ideaDescription && ideaDescription.length > 300) {
        return res.status(400).json({
          error: true,
          reason: "Idea description must be less than 300 characters"
        })
      }

      // Validate special requirements
      if (specialRequirements && specialRequirements.length > 300) {
        return res.status(400).json({
          error: true,
          reason: "Special requirements must be less than 300 characters"
        })
      }

      // Get the user
      const user = await User.findOne({ _id: userId })

      if (!user) {
        return res.status(404).json({
          error: true,
          reason: "User not found"
        })
      }

      const platform = await Platform.findOne({ _id: id, primaryUser: userId }).exec()

      // Check if user has a primary platform
      // if (user.platforms && user.platforms.length > 0) {
      //   // Try to find a platform where this user is the primary user
      //   platform = await Platform.findOne({
      //     primaryUser: userId
      //   })
      // }

      // if (!platform) {
      // Make sure the user has userType set to Owner
      // await User.updateOne({ _id: userId }, { userType: "Owner" })

      // Create new platform for this user
      // platform = new Platform({
      //   primaryUser: userId,
      //   teamMembers: [userId] // Add the owner as a team member
      // })

      // Update user with reference to the new platform
      // if (!user.platforms) {
      //   user.platforms = []
      // }
      // user.platforms.push(platform._id)
      // await user.save()
      // }

      // Update platform fields if provided
      if (name) platform.name = name
      if (colorPalette) platform.colorPalette = colorPalette
      if (logo) platform.logo = logo
      if (socialProfiles) platform.socialProfiles = socialProfiles
      if (ideaDescription !== undefined) platform.ideaDescription = ideaDescription
      if (existingWebsite !== undefined) platform.existingWebsite = existingWebsite
      if (specialRequirements !== undefined) platform.specialRequirements = specialRequirements

      // Update launch status if provided or if currently at setup stage
      if (launchStatus) {
        platform.launchStatus = launchStatus
      }

      await platform.save()

      // Send setup-started email to the user
      try {
        await mail("setup-started", {
          to: user.email,
          subject: "We've Received Your Platform Info – Setup in Progress 🛠️",
          locals: {
            firstName: user.name.first,
            platformName: "Vewmee White Level"
          }
        })
        console.log(`Setup started email sent to ${user.email}`)
      } catch (emailError) {
        console.error("Error sending setup started email:", emailError)
        // Continue execution even if email fails
      }

      return res.status(200).json({
        error: false,
        data: {
          platform
        }
      })
    } catch (error) {
      return res.status(500).json({
        error: true,
        reason: error.message
      })
    }
  },

  /**
   * @swagger
   * /platform/comments:
   *   post:
   *     summary: Add a new comment or reply to an existing comment
   *     tags: [Platform]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - content
   *               - platformId
   *             properties:
   *               content:
   *                 type: string
   *                 description: Comment content
   *               section:
   *                 type: string
   *                 enum: [Creator Login, Creator Sign Up, Fan Website, Fan Sign Up, Fan Login, Fan Feed]
   *                 description: Section the comment belongs to
   *                 default: Fan Website
   *               parentComment:
   *                 type: string
   *                 description: Parent comment ID for replies (only for replying to existing comments)
   *               platformId:
   *                 type: string
   *                 description: ID of the platform the comment belongs to
   *           examples:
   *             new_comment:
   *               summary: Example for adding a new comment
   *               value:
   *                 content: "I like the design of the homepage"
   *                 section: "website"
   *                 platformId: "60d21b4667d0d8992e610c85"
   *             reply:
   *               summary: Example for adding a reply to an existing comment
   *               value:
   *                 content: "Thank you for your feedback! We'll continue to improve it."
   *                 parentComment: "60d21b4667d0d8992e610c86"
   *                 platformId: "60d21b4667d0d8992e610c85"
   *     responses:
   *       200:
   *         description: Comment added successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     comment:
   *                       type: object
   *                       properties:
   *                         _id:
   *                           type: string
   *                           example: "60d21b4667d0d8992e610c85"
   *                         content:
   *                           type: string
   *                           example: "I like the design of the homepage"
   *                         section:
   *                           type: string
   *                           example: "website"
   *                         parentComment:
   *                           type: string
   *                           example: null
   *                           description: Will be null for top-level comments, contains parent comment ID for replies
   *                         replies:
   *                           type: array
   *                           description: Array of reply IDs (only for top-level comments)
   *                           items:
   *                             type: string
   *                           example: []
   *                         createdAt:
   *                           type: string
   *                           format: date-time
   *                           example: "2023-01-01T00:00:00.000Z"
   *                         updatedAt:
   *                           type: string
   *                           format: date-time
   *                           example: "2023-01-01T00:00:00.000Z"
   *                         user:
   *                           type: object
   *                           properties:
   *                             _id:
   *                               type: string
   *                               example: "60d21b4667d0d8992e610c85"
   *                             name:
   *                               type: object
   *                               properties:
   *                                 first:
   *                                   type: string
   *                                   example: "John"
   *                                 last:
   *                                   type: string
   *                                   example: "Doe"
   *                             imageUrl:
   *                               type: string
   *                               example: "https://example.com/profile.jpg"
   *                             email:
   *                               type: string
   *                               example: "<EMAIL>"
   *                             fullName:
   *                               type: string
   *                               example: "John Doe"
   *             examples:
   *               new_comment_response:
   *                 summary: Response when adding a new comment
   *                 value:
   *                   error: false
   *                   data:
   *                     comment:
   *                       _id: "60d21b4667d0d8992e610c85"
   *                       content: "I like the design of the homepage"
   *                       section: "website"
   *                       parentComment: null
   *                       replies: []
   *                       createdAt: "2023-01-01T00:00:00.000Z"
   *                       updatedAt: "2023-01-01T00:00:00.000Z"
   *                       user:
   *                         _id: "60d21b4667d0d8992e610c85"
   *                         name:
   *                           first: "John"
   *                           last: "Doe"
   *                         imageUrl: "https://example.com/profile.jpg"
   *                         email: "<EMAIL>"
   *                         fullName: "John Doe"
   *               reply_response:
   *                 summary: Response when adding a reply to a comment
   *                 value:
   *                   error: false
   *                   data:
   *                     comment:
   *                       _id: "60d21b4667d0d8992e610c86"
   *                       content: "Thank you for your feedback! We'll continue to improve it."
   *                       section: "website"
   *                       parentComment: "60d21b4667d0d8992e610c85"
   *                       replies: []
   *                       createdAt: "2023-01-01T00:00:00.000Z"
   *                       updatedAt: "2023-01-01T00:00:00.000Z"
   *                       user:
   *                         _id: "60d21b4667d0d8992e610c87"
   *                         name:
   *                           first: "Jane"
   *                           last: "Smith"
   *                         imageUrl: "https://example.com/jane-profile.jpg"
   *                         email: "<EMAIL>"
   *                         fullName: "Jane Smith"
   */
  async addComment(req, res) {
    try {
      const userId = req.user.id
      const {
        content, section = "Fan Website", parentComment, platformId
      } = req.body

      if (!content) {
        return res.status(400).json({
          error: true,
          reason: "Comment content is required"
        })
      }

      if (!platformId) {
        return res.status(400).json({
          error: true,
          reason: "Platform ID is required"
        })
      }

      // Get user
      const user = await User.findById(userId)
      if (!user) {
        return res.status(404).json({
          error: true,
          reason: "User not found"
        })
      }

      // Get the platform
      const platform = await Platform.findById(platformId)
      if (!platform) {
        return res.status(404).json({
          error: true,
          reason: "Platform not found"
        })
      }

      // Check if user has access to this platform
      // For regular users, check if they are in the platform's team members
      // For team members, check if the platform is in their _platform array
      let hasAccess = false

      if (user.userType === "TeamMember") {
        // For team members login, check if platform contains those teamMembers
        hasAccess = platform.teamMembers.some((id) => id.toString() === userId)
      } else {
        // For onwer login, check if they are the primary user
        hasAccess = platform.primaryUser.toString() === userId
      }

      if (!hasAccess) {
        return res.status(403).json({
          error: true,
          reason: "You don't have access to this platform"
        })
      }

      // Check if this is a reply to an existing comment
      if (parentComment) {
        // Find the parent comment
        const parent = await Comment.findOne({ _id: parentComment })

        if (!parent) {
          return res.status(404).json({
            error: true,
            reason: "Parent comment not found"
          })
        }

        // Create reply
        const reply = await Comment.create({
          user: userId,
          section: parent.section, // Use the same section as parent
          content,
          parentComment,
          platform: parent.platform // Inherit platform from parent
        })

        // Update parent comment's replies array
        await Comment.updateOne(
          { _id: parentComment },
          { $push: { replies: reply._id } }
        )

        // Populate user information
        const populatedReply = await Comment.findOne({ _id: reply._id })
          .populate({
            path: "user",
            select: "name imageUrl email fullName"
          })

        return res.json({
          error: false,
          data: {
            comment: populatedReply
          }
        })
      }

      // This is a top-level comment
      const comment = await Comment.create({
        user: userId,
        section,
        content,
        parentComment: null,
        platform: platform._id
      })

      // Populate user information
      const populatedComment = await Comment.findOne({ _id: comment._id })
        .populate({
          path: "user",
          select: "name imageUrl email fullName"
        })

      return res.json({
        error: false,
        data: {
          comment: populatedComment
        }
      })
    } catch (error) {
      return res.status(500).json({
        error: true,
        reason: error.message
      })
    }
  },

  /**
   * @swagger
   * /platform/comments:
   *   get:
   *     summary: Get all comments for the platform
   *     description: Retrieves all comments for the platform with optional section filtering
   *     tags: [Platform]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: query
   *         name: platformId
   *         required: true
   *         schema:
   *           type: string
   *         description: ID of the platform to get comments for
   *       - in: query
   *         name: section
   *         required: false
   *         schema:
   *           type: string
   *           enum: ["Creator Login", "Creator Sign Up", "Fan Website", "Fan Sign Up", "Fan Login", "Fan Feed"]
   *         description: Optional filter for comments by section. If not provided, returns comments from all sections.
   *     responses:
   *       200:
   *         description: Comments retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     comments:
   *                       type: array
   *                       items:
   *                         type: object
   *                         properties:
   *                           _id:
   *                             type: string
   *                             example: "60d21b4667d0d8992e610c85"
   *                           content:
   *                             type: string
   *                             example: "I like the design of the homepage"
   *                           section:
   *                             type: string
   *                             example: "Fan Website"
   *                           parentComment:
   *                             type: string
   *                             example: null
   *                             description: Will be null for top-level comments
   *                           replies:
   *                             type: array
   *                             description: Array of reply objects
   *                             items:
   *                               type: object
   *                               properties:
   *                                 _id:
   *                                   type: string
   *                                   example: "60d21b4667d0d8992e610c86"
   *                                 content:
   *                                   type: string
   *                                   example: "Thank you for your feedback! We'll continue to improve it."
   *                                 section:
   *                                   type: string
   *                                   example: "Fan Website"
   *                                 parentComment:
   *                                   type: string
   *                                   example: "60d21b4667d0d8992e610c85"
   *                                 user:
   *                                   type: object
   *                                   properties:
   *                                     _id:
   *                                       type: string
   *                                       example: "60d21b4667d0d8992e610c87"
   *                                     name:
   *                                       type: object
   *                                       properties:
   *                                         first:
   *                                           type: string
   *                                           example: "Jane"
   *                                         last:
   *                                           type: string
   *                                           example: "Smith"
   *                                     imageUrl:
   *                                       type: string
   *                                       example: "https://example.com/jane-profile.jpg"
   *                                     email:
   *                                       type: string
   *                                       example: "<EMAIL>"
   *                                     fullName:
   *                                       type: string
   *                                       example: "Jane Smith"
   *                                 createdAt:
   *                                   type: string
   *                                   format: date-time
   *                                   example: "2023-01-01T00:00:00.000Z"
   *                           user:
   *                             type: object
   *                             properties:
   *                               _id:
   *                                 type: string
   *                                 example: "60d21b4667d0d8992e610c85"
   *                               name:
   *                                 type: object
   *                                 properties:
   *                                   first:
   *                                     type: string
   *                                     example: "John"
   *                                   last:
   *                                     type: string
   *                                     example: "Doe"
   *                               imageUrl:
   *                                 type: string
   *                                 example: "https://example.com/profile.jpg"
   *                               email:
   *                                 type: string
   *                                 example: "<EMAIL>"
   *                               fullName:
   *                                 type: string
   *                                 example: "John Doe"
   *                           createdAt:
   *                             type: string
   *                             format: date-time
   *                             example: "2023-01-01T00:00:00.000Z"
   *           example:
   *             error: false
   *             data:
   *               comments:
   *                 - _id: "60d21b4667d0d8992e610c85"
   *                   content: "I like the design of the homepage"
   *                   section: "Fan Website"
   *                   parentComment: null
   *                   replies:
   *                     - _id: "60d21b4667d0d8992e610c86"
   *                       content: "Thank you for your feedback! We'll continue to improve it."
   *                       section: "Fan Website"
   *                       parentComment: "60d21b4667d0d8992e610c85"
   *                       user:
   *                         _id: "60d21b4667d0d8992e610c87"
   *                         name:
   *                           first: "Jane"
   *                           last: "Smith"
   *                         imageUrl: "https://example.com/jane-profile.jpg"
   *                         email: "<EMAIL>"
   *                         fullName: "Jane Smith"
   *                       createdAt: "2023-01-01T00:00:00.000Z"
   *                       updatedAt: "2023-01-01T00:00:00.000Z"
   *                   user:
   *                     _id: "60d21b4667d0d8992e610c85"
   *                     name:
   *                       first: "John"
   *                       last: "Doe"
   *                     imageUrl: "https://example.com/profile.jpg"
   *                     email: "<EMAIL>"
   *                     fullName: "John Doe"
   *                   createdAt: "2023-01-01T00:00:00.000Z"
   *                   updatedAt: "2023-01-01T00:00:00.000Z"
   *                 - _id: "60d21b4667d0d8992e610c88"
   *                   content: "The mobile responsiveness could be improved"
   *                   section: "Fan Website"
   *                   parentComment: null
   *                   replies: []
   *                   user:
   *                     _id: "60d21b4667d0d8992e610c89"
   *                     name:
   *                       first: "Alex"
   *                       last: "Johnson"
   *                     imageUrl: "https://example.com/alex-profile.jpg"
   *                     email: "<EMAIL>"
   *                     fullName: "Alex Johnson"
   *                   createdAt: "2023-01-02T00:00:00.000Z"
   *                   updatedAt: "2023-01-02T00:00:00.000Z"
   */
  async getComments(req, res) {
    try {
      const userId = req.user.id
      const { section, platformId } = req.query

      if (!platformId) {
        return res.status(400).json({
          error: true,
          reason: "`platformId` is required"
        })
      }

      // Get user
      const user = await User.findById(userId)
      if (!user) {
        return res.status(404).json({
          error: true,
          reason: "User not found"
        })
      }

      // Get the platform
      const platform = await Platform.findById(platformId)
      if (!platform) {
        return res.status(404).json({
          error: true,
          reason: "Platform not found"
        })
      }

      // Check if user has access to this platform
      let hasAccess = false

      if (user.userType === "TeamMember") {
        // For team members, check if platform has team member in teamMemebrs array
        hasAccess = platform.teamMembers.some((id) => id.toString() === platformId)
      } else {
        // For owner, check if they are the primary user
        hasAccess = platform.primaryUser.toString() === userId
      }

      if (!hasAccess) {
        return res.status(403).json({
          error: true,
          reason: "You don't have access to this platform"
        })
      }

      // Build query for top-level comments
      const query = {
        parentComment: null,
        platform: platform._id
      }

      // Add section filter if provided
      if (section) {
        query.section = section
      }

      // Get top-level comments
      const comments = await Comment.find(query)
        .populate({
          path: "user",
          select: "name imageUrl email fullName"
        })
        .populate({
          path: "replies",
          populate: {
            path: "user",
            select: "name imageUrl email fullName"
          }
        })
        .sort("-createdAt")

      // Function to recursively populate nested replies
      const populateNestedReplies = async (commentList) => {
        await Promise.all(commentList.map(async (comment) => {
          if (comment.replies && comment.replies.length > 0) {
            // Populate second-level replies
            await Promise.all(comment.replies.map(async (reply) => {
              if (reply.replies && reply.replies.length > 0) {
                const nestedReplies = await Comment.find({
                  _id: { $in: reply.replies }
                })
                  .populate({
                    path: "user",
                    select: "name imageUrl email fullName"
                  })
                  .sort("createdAt")

                // Create a copy instead of modifying the parameter directly
                const updatedReply = { ...reply }
                updatedReply.replies = nestedReplies

                // Continue populating deeper levels if needed
                await populateNestedReplies(nestedReplies)
                return updatedReply
              }
              return reply
            }))
          }
          return comment
        }))
        return commentList
      }

      // Populate any nested replies
      await populateNestedReplies(comments)

      return res.json({
        error: false,
        data: {
          comments
        }
      })
    } catch (error) {
      console.log(error)
      return res.status(500).json({
        error: true,
        reason: error.message
      })
    }
  },

  /**
   * @swagger
   * /platform/comments/{id}:
   *   put:
   *     summary: Update a comment
   *     tags: [Platform]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Comment ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - content
   *               - platformId
   *             properties:
   *               content:
   *                 type: string
   *                 description: Updated comment content
   *               platformId:
   *                 type: string
   *                 description: ID of the platform the comment belongs to
   *           examples:
   *             update_comment:
   *               summary: Example for updating a comment
   *               value:
   *                 content: "Updated comment content - I really like the new design!"
   *                 platformId: "60d21b4667d0d8992e610c85"
   *             update_reply:
   *               summary: Example for updating a reply
   *               value:
   *                 content: "Updated reply - Thanks for your feedback, we've made those improvements!"
   *                 platformId: "60d21b4667d0d8992e610c85"
   *     responses:
   *       200:
   *         description: Comment updated successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     comment:
   *                       type: object
   *                       properties:
   *                         _id:
   *                           type: string
   *                           example: "60d21b4667d0d8992e610c85"
   *                         content:
   *                           type: string
   *                           example: "Updated comment content"
   *                         section:
   *                           type: string
   *                           example: "Fan Website"
   *                         parentComment:
   *                           type: string
   *                           example: null
   *                           description: Will be null for top-level comments, contains parent comment ID for replies
   *                         replies:
   *                           type: array
   *                           description: Array of reply IDs (only for top-level comments)
   *                           items:
   *                             type: string
   *                           example: []
   *                         user:
   *                           type: object
   *                           properties:
   *                             _id:
   *                               type: string
   *                               example: "60d21b4667d0d8992e610c85"
   *                             name:
   *                               type: object
   *                               properties:
   *                                 first:
   *                                   type: string
   *                                   example: "John"
   *                                 last:
   *                                   type: string
   *                                   example: "Doe"
   *                             imageUrl:
   *                               type: string
   *                               example: "https://example.com/profile.jpg"
   *                             email:
   *                               type: string
   *                               example: "<EMAIL>"
   *                             fullName:
   *                               type: string
   *                               example: "John Doe"
   *                         createdAt:
   *                           type: string
   *                           format: date-time
   *                           example: "2023-01-01T00:00:00.000Z"
   *                         updatedAt:
   *                           type: string
   *                           format: date-time
   *                           example: "2023-01-01T00:00:00.000Z"
   *             examples:
   *               update_comment_response:
   *                 summary: Response when updating a comment
   *                 value:
   *                   error: false
   *                   data:
   *                     comment:
   *                       _id: "60d21b4667d0d8992e610c85"
   *                       content: "Updated comment content - I really like the new design!"
   *                       section: "Fan Website"
   *                       parentComment: null
   *                       replies: ["60d21b4667d0d8992e610c86"]
   *                       user:
   *                         _id: "60d21b4667d0d8992e610c85"
   *                         name:
   *                           first: "John"
   *                           last: "Doe"
   *                         imageUrl: "https://example.com/profile.jpg"
   *                         email: "<EMAIL>"
   *                         fullName: "John Doe"
   *                       createdAt: "2023-01-01T00:00:00.000Z"
   *                       updatedAt: "2023-01-01T01:30:00.000Z"
   *               update_reply_response:
   *                 summary: Response when updating a reply
   *                 value:
   *                   error: false
   *                   data:
   *                     comment:
   *                       _id: "60d21b4667d0d8992e610c86"
   *                       content: "Updated reply - Thanks for your feedback, we've made those improvements!"
   *                       section: "Fan Website"
   *                       parentComment: "60d21b4667d0d8992e610c85"
   *                       replies: []
   *                       user:
   *                         _id: "60d21b4667d0d8992e610c87"
   *                         name:
   *                           first: "Jane"
   *                           last: "Smith"
   *                         imageUrl: "https://example.com/jane-profile.jpg"
   *                         email: "<EMAIL>"
   *                         fullName: "Jane Smith"
   *                       createdAt: "2023-01-01T00:00:00.000Z"
   *                       updatedAt: "2023-01-01T02:15:00.000Z"
   *       400:
   *         description: Bad request - validation error
   *       403:
   *         description: Not authorized to update this comment
   *       404:
   *         description: Comment not found
   *       500:
   *         description: Server error
   */
  async updateComment(req, res) {
    try {
      const userId = req.user.id
      const { id } = req.params
      const { content, section, platformId } = req.body

      // Validate content
      if (!content) {
        return res.status(400).json({
          error: true,
          reason: "Comment content is required"
        })
      }

      if (content.length > 500) {
        return res.status(400).json({
          error: true,
          reason: "Comment content must be less than 500 characters"
        })
      }

      if (!platformId) {
        return res.status(400).json({
          error: true,
          reason: "Platform ID is required"
        })
      }

      // Find the comment
      const comment = await Comment.findOne({ _id: id }).populate({
        path: "user",
        select: "name imageUrl email fullName"
      })

      if (!comment) {
        return res.status(404).json({
          error: true,
          reason: "Comment not found"
        })
      }

      // Verify the comment belongs to the specified platform
      if (comment.platform.toString() !== platformId) {
        return res.status(403).json({
          error: true,
          reason: "Comment does not belong to the specified platform"
        })
      }

      // Get user
      const user = await User.findById(userId)
      if (!user) {
        return res.status(404).json({
          error: true,
          reason: "User not found"
        })
      }
      const platform = await Platform.findById(platformId)
      if (!platform) {
        return res.status(404).json({
          error: true,
          reason: "Platform not found"
        })
      }

      // Check if user has access to this platform
      let hasAccess = false

      if (user.userType === "TeamMember") {
        // For team members, check if platform has team member in teamMemebrs array
        hasAccess = platform.teamMembers.some((elemId) => elemId.toString() === platformId)
      } else {
        // For owner, check if they are the primary user
        hasAccess = platform.primaryUser.toString() === userId
      }

      if (!hasAccess) {
        return res.status(403).json({
          error: true,
          reason: "You don't have access to this platform"
        })
      }
      // Check if the user is the comment owner
      if (comment.user._id.toString() !== userId) {
        return res.status(403).json({
          error: true,
          reason: "You are not authorized to update this comment"
        })
      }

      // Update the comment
      comment.content = content

      // Update section if provided
      if (section) {
        comment.section = section
      }

      await comment.save()

      // Populate user information
      // const updatedComment = await Comment.findOne({ _id: id })
      //   .populate({
      //     path: "user",
      //     select: "name imageUrl email fullName"
      //   })

      return res.json({
        error: false,
        data: {
          comment
        }
      })
    } catch (error) {
      return res.status(500).json({
        error: true,
        reason: error.message
      })
    }
  },

  /**
   * @swagger
   * /platform/comments/{id}:
   *   delete:
   *     summary: Delete a comment or reply
   *     tags: [Platform]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Comment ID
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - platformId
   *             properties:
   *               platformId:
   *                 type: string
   *                 description: ID of the platform the comment belongs to
   *           examples:
   *             delete_comment:
   *               summary: Example for deleting a comment
   *               value:
   *                 platformId: "60d21b4667d0d8992e610c85"
   *             delete_reply:
   *               summary: Example for deleting a reply
   *               value:
   *                 platformId: "60d21b4667d0d8992e610c85"
   *     responses:
   *       200:
   *         description: Comment or reply deleted successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     message:
   *                       type: string
   *                       example: "Comment deleted successfully"
   *             examples:
   *               delete_comment_response:
   *                 summary: Response when deleting a comment
   *                 value:
   *                   error: false
   *                   data:
   *                     message: "Comment deleted successfully"
   *               delete_reply_response:
   *                 summary: Response when deleting a reply
   *                 value:
   *                   error: false
   *                   data:
   *                     message: "Reply deleted successfully"
   *       403:
   *         description: Forbidden - not the comment owner
   *       404:
   *         description: Comment not found
   *       500:
   *         description: Server error
   */
  async deleteComment(req, res) {
    try {
      const userId = req.user.id
      const { id } = req.params
      const { platformId } = req.body

      if (!platformId) {
        return res.status(400).json({
          error: true,
          reason: "Platform ID is required"
        })
      }

      // Find the comment
      const comment = await Comment.findOne({ _id: id })

      if (!comment) {
        return res.status(404).json({
          error: true,
          reason: "Comment not found"
        })
      }

      // Verify the comment belongs to the specified platform
      if (comment.platform.toString() !== platformId) {
        return res.status(403).json({
          error: true,
          reason: "Comment does not belong to the specified platform"
        })
      }

      // Get user
      const user = await User.findById(userId)
      if (!user) {
        return res.status(404).json({
          error: true,
          reason: "User not found"
        })
      }

      const platform = await Platform.findById(platformId)
      if (!platform) {
        return res.status(404).json({
          error: true,
          reason: "Platform not found"
        })
      }
      // Check if user has access to this platform
      // For regular users, check if they are in the platform's team members
      // For team members, check if the platform is in their _platform array
      let hasAccess = false

      if (user.userType === "TeamMember") {
        // For team members, check if platform has team member in teamMemebrs array
        hasAccess = platform.teamMembers.some((elemId) => elemId.toString() === platformId)
      } else {
        // For owner, check if they are the primary user
        hasAccess = platform.primaryUser.toString() === userId
      }

      if (!hasAccess) {
        return res.status(403).json({
          error: true,
          reason: "You don't have access to this platform"
        })
      }

      // Check if the user is the comment owner
      if (comment.user.toString() !== userId) {
        return res.status(403).json({
          error: true,
          reason: "You are not authorized to delete this comment"
        })
      }

      const isReply = !!comment.parentComment

      // If this is a reply, remove it from the parent's replies array
      if (isReply) {
        await Comment.updateOne(
          { _id: comment.parentComment },
          { $pull: { replies: comment._id } }
        )
      }

      // If this comment has replies, delete them as well
      if (comment.replies && comment.replies.length > 0) {
        await Comment.deleteMany({ _id: { $in: comment.replies } })
      }

      // Delete the main comment or reply
      await Comment.deleteOne({ _id: id })

      return res.json({
        error: false,
        data: {
          message: isReply
            ? "Reply deleted successfully"
            : "Comment deleted successfully"
        }
      })
    } catch (error) {
      return res.status(500).json({
        error: true,
        reason: error.message
      })
    }
  },

  /**
   * @swagger
   * /platform/generatelogo:
   *   post:
   *     summary: Generate logo images using AI
   *     description: Uses Gemini AI to create logo prompts and Replicate API to generate logo images based on the platform name
   *     tags: [Platform]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - platformName
   *             properties:
   *               platformName:
   *                 type: string
   *                 description: Company/platform name for logo generation
   *           examples:
   *             tech_company:
   *               summary: Example for a tech company
   *               value:
   *                 platformName: "TechCorp"
   *             creative_agency:
   *               summary: Example for a creative agency
   *               value:
   *                 platformName: "CreativeMinds"
   *             health_platform:
   *               summary: Example for a health platform
   *               value:
   *                 platformName: "HealthWave"
   *     responses:
   *       200:
   *         description: Logo images generated successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     prompts:
   *                       type: array
   *                       items:
   *                         type: string
   *                       description: AI-generated prompts used to create the logos
   *                     imageUrls:
   *                       type: array
   *                       items:
   *                         type: string
   *                       description: URLs to the generated logo images
   *             examples:
   *               tech_company_response:
   *                 summary: Response for a tech company
   *                 value:
   *                   error: false
   *                   data:
   *                     prompts: [
   *                       "A sleek, minimalist logo featuring a blue and purple gradient abstract shape resembling a tech circuit or neural network",
   *                       "A bold, modern logo with a green hexagon containing a stylized letter 'T' in white, representing technology and innovation",
   *                       "A dynamic logo with red and orange flame-like elements forming a tech-inspired icon, symbolizing energy and transformation"
   *                     ]
   *                     imageUrls: [
   *                       "https://example.com/logos/techcorp_logo1.png",
   *                       "https://example.com/logos/techcorp_logo2.png",
   *                       "https://example.com/logos/techcorp_logo3.png"
   *                     ]
   *               creative_agency_response:
   *                 summary: Response for a creative agency
   *                 value:
   *                   error: false
   *                   data:
   *                     prompts: [
   *                       "An artistic logo with paint brush strokes in vibrant colors forming an abstract 'C' shape",
   *                       "A clean, elegant logo with a golden ratio spiral and the text 'CreativeMinds' in a modern sans-serif font",
   *                       "A playful logo with colorful geometric shapes arranged to form a lightbulb, symbolizing ideas and creativity"
   *                     ]
   *                     imageUrls: [
   *                       "https://example.com/logos/creativeminds_logo1.png",
   *                       "https://example.com/logos/creativeminds_logo2.png",
   *                       "https://example.com/logos/creativeminds_logo3.png"
   *                     ]
   *       400:
   *         description: Bad request - company name is required
   *       500:
   *         description: Server error
   */
  async generateLogo(req, res) {
    try {
      const { platformName } = req.body

      if (!platformName) {
        return res.status(400).json({
          error: true,
          reason: "Company name is required"
        })
      }

      // Generate prompts using Gemini
      // const prompts = await generatePrompts(platformName);

      const prompts = [
        "A sleek, minimalist logo featuring a blue and purple gradient abstract shape resembling a tech circuit or neural network",
        "A bold, modern logo with a green hexagon containing a stylized letter 'T' in white, representing technology and innovation",
        "A dynamic logo with red and orange flame-like elements forming a tech-inspired icon, symbolizing energy and transformation"
      ]

      console.log("Generated prompts:", prompts)

      // Generate images from prompts using Replicate
      const imageUrls = await generateImagesFromPrompts(prompts)

      return res.status(200).json({
        error: false,
        data: {
          prompts,
          imageUrls
        }
      })
    } catch (error) {
      console.error("Logo generation error:", error)
      return res.status(500).json({
        error: true,
        reason: error.message || "An error occurred while generating logo images"
      })
    }
  },

  /**
   * @swagger
   * /platform/senddetails:
   *   post:
   *     summary: Send platform details via email
   *     description: Sends an email with platform links (website, fan app, creator portal) to the user or specified email address
   *     tags: [Platform]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: false
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               email:
   *                 type: string
   *                 description: Optional email address to send to (defaults to user's email)
   *               platformId:
   *                 type: string
   *                 description: Optional platform ID (defaults to user's primary platform)
   *           examples:
   *             default_email:
   *               summary: Send to user's own email
   *               value: {}
   *             custom_email:
   *               summary: Send to a specific email address
   *               value:
   *                 email: "<EMAIL>"
   *             specific_platform:
   *               summary: Send details for a specific platform
   *               value:
   *                 email: "<EMAIL>"
   *                 platformId: "60d21b4667d0d8992e610c85"
   *     responses:
   *       200:
   *         description: Email sent successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     message:
   *                       type: string
   *                       example: "Platform details email sent successfully"
   *             examples:
   *               success_response:
   *                 summary: Successful email sending
   *                 value:
   *                   error: false
   *                   data:
   *                     message: "Platform details email sent successfully"
   *               development_response:
   *                 summary: Response in development environment (no actual email sent)
   *                 value:
   *                   error: false
   *                   data:
   *                     message: "Platform details email would be sent in production"
   *                     emailData: {
   *                       to: "<EMAIL>",
   *                       subject: "Your Platform Links",
   *                       creatorName: "John Doe",
   *                       website: "https://example-platform.com",
   *                       fanAppLink: "https://fan.example-platform.com",
   *                       creatorLink: "https://creator.example-platform.com"
   *                     }
   *       404:
   *         description: User or platform not found
   *       500:
   *         description: Server error
   */
  async sendPlatformDetails(req, res) {
    try {
      const userId = req.user.id
      const { email } = req.body

      // Get user and platform
      const user = await User.findById(userId)
      if (!user) {
        return res.status(404).json({
          error: true,
          reason: "User not found"
        })
      }
      // Check if user has a platform
      if (!user.platforms || user.platforms.length === 0) {
        return res.status(404).json({
          error: true,
          reason: "Platform not found"
        })
      }

      // Get the platform
      const platform = await Platform.findOne({
        _id: { $in: user.platforms }
      })

      if (!platform) {
        return res.status(404).json({
          error: true,
          reason: "Platform not found"
        })
      }

      // Get creator's name
      const creatorName = user.name && (user.name.first || user.name.last)
        ? `${user.name.first || ""} ${user.name.last || ""}`.trim()
        : ""

      // Determine email recipient
      const emailTo = email || user.email

      // Send email with platform links
      try {
        await mail("platform-details", {
          to: emailTo,
          subject: "Your Platform Links",
          locals: {
            creatorName,
            website: platform.website || "",
            fanAppLink: platform.fanAppLink || "",
            creatorLink: platform.creatorLink || ""
          }
        })
        console.log(`Platform details email sent to ${emailTo}`)
      } catch (emailError) {
        console.error(`Error sending platform details email to ${emailTo}:`, emailError)
        throw new Error("Failed to send platform details email")
      }

      return res.json({
        error: false,
        data: {
          message: "Platform details email sent successfully"
        }
      })
    } catch (error) {
      console.error("Error sending platform details email:", error)
      return res.status(500).json({
        error: true,
        reason: error.message
      })
    }
  },

  /**
   * @swagger
   * /platform/{id}:
   *   get:
   *     summary: Get platform details
   *     description: Retrieve the details of a specific platform associated with the authenticated user.
   *     tags: [Platform]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: ID of the platform to retrieve
   *     responses:
   *       200:
   *         description: Platform details retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     platform:
   *                       type: object
   *                       properties:
   *                         _id:
   *                           type: string
   *                           example: "60d21b4667d0d8992e610c85"
   *                         name:
   *                           type: string
   *                           example: "My Platform"
   *                         colorPalette:
   *                           type: string
   *                           example: "blue"
   *                         logo:
   *                           type: string
   *                           example: "https://example.com/logo.png"
   *                         socialProfiles:
   *                           type: object
   *                           example: { "twitter": "myhandle", "instagram": "myhandle" }
   *                         ideaDescription:
   *                           type: string
   *                           example: "A platform for connecting creators with fans"
   *                         existingWebsite:
   *                           type: string
   *                           example: "https://myexistingsite.com"
   *                         specialRequirements:
   *                           type: string
   *                           example: "Need mobile-friendly design"
   *                         launchStatus:
   *                           type: number
   *                           example: 2
   *                         website:
   *                           type: string
   *                           example: "https://myplatform.com"
   *                         fanAppLink:
   *                           type: string
   *                           example: "https://fanapp.myplatform.com"
   *                         creatorLink:
   *                           type: string
   *                           example: "https://creator.myplatform.com"
   *       400:
   *         description: Bad request - missing mandatory parameters
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 reason:
   *                   type: string
   *                   example: "missing mandatory params `id`"
   *       404:
   *         description: User or platform not found
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 reason:
   *                   type: string
   *                   example: "User not found"
   *       500:
   *         description: Server error
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 reason:
   *                   type: string
   *                   example: "Internal server error"
   */
  async get(req, res) {
    try {
      const { id } = req.params
      if (!id) {
        return res.status(400).json({
          error: true,
          reason: "missing mandatory params `id`"
        })
      }
      const user = await User.findById(req.user._id)
      if (!user) {
        return res.status(404).json({
          error: true,
          reason: "User not found"
        })
      }
      const platform = await Platform.findOne({
        _id: id
      })
      if (!platform) {
        return res.status(404).json({
          error: true,
          reason: "Platform not found"
        })
      }
      return res.status(200).json({
        error: false,
        data: { platform }
      })
    } catch (error) {
      console.error("Error getting platform:", error)
      return res.status(500).json({
        error: true,
        reason: error.message
      })
    }
  }
}
