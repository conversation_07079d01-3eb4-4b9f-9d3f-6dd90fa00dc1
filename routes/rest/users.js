/* eslint-disable prefer-destructuring */
/* eslint-disable max-len */
const { STS } = require("aws-sdk")
const {
  User, Platform, TeamMember, Owner
} = require("../../models")
const mail = require("../../lib/mail")

module.exports = {

  /**
   * @swagger
   * /addMembers:
   *   post:
   *     summary: Add a team member (with isOwner=false)
   *     description: Creates a new user with isOwner=false and adds them to the current user's team and platform
   *     tags: [User]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - name
   *               - email
   *               - phone
   *               - address
   *               - platformId
   *             properties:
   *               name:
   *                 type: string
   *                 description: Full name of the team member (will be split into first and last name)
   *               email:
   *                 type: string
   *                 description: Email of the team member
   *               phone:
   *                 type: string
   *                 description: Phone number of the team member
   *               address:
   *                 type: string
   *                 description: Address of the team member
   *               imageUrl:
   *                 type: string
   *                 description: Profile image URL of the team member (optional)
   *               platformId:
   *                 type: string
   *                 description: ID of the platform to add the team member to
   *           examples:
   *             basic_example:
   *               summary: Basic example with required fields
   *               value:
   *                 name: "<PERSON>"
   *                 email: "<EMAIL>"
   *                 phone: "1234567890"
   *                 address: "123 Main St, City, Country"
   *                 platformId: "60d21b4667d0d8992e610c85"
   *             full_example:
   *               summary: Complete example with all fields
   *               value:
   *                 name: "John Doe"
   *                 email: "<EMAIL>"
   *                 phone: "1234567890"
   *                 address: "123 Main St, City, Country"
   *                 imageUrl: "https://example.com/profile.jpg"
   *                 platformId: "60d21b4667d0d8992e610c85"
   *     responses:
   *       200:
   *         description: Team member added successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     message:
   *                       type: string
   *                       example: "Team member added successfully"
   *                     teamMember:
   *                       type: object
   *                       properties:
   *                         id:
   *                           type: string
   *                           example: "60d21b4667d0d8992e610c86"
   *                         email:
   *                           type: string
   *                           example: "<EMAIL>"
   *                         name:
   *                           type: object
   *                           properties:
   *                             first:
   *                               type: string
   *                               example: "John"
   *                             last:
   *                               type: string
   *                               example: "Doe"
   *                         phone:
   *                           type: string
   *                           example: "1234567890"
   *                         address:
   *                           type: string
   *                           example: "123 Main St, City, Country"
   *                         imageUrl:
   *                           type: string
   *                           example: "https://example.com/profile.jpg"
   *                         isOwner:
   *                           type: boolean
   *                           example: false
   *                           description: Always false for team members added through this endpoint
   *                     welcomeEmail:
   *                       type: object
   *                       description: Information about the welcome email (only in development environment)
   *                       properties:
   *                         sent:
   *                           type: boolean
   *                           example: false
   *                           description: Whether the welcome email was sent
   *                         message:
   *                           type: string
   *                           example: "Development mode: Welcome email not sent"
   *                           description: Status message about the welcome email
   *                         loginDetails:
   *                           type: object
   *                           description: Login details (only in development environment)
   *                           properties:
   *                             email:
   *                               type: string
   *                               example: "<EMAIL>"
   *                             password:
   *                               type: string
   *                               example: "Qwerty@123"
   *                             loginUrl:
   *                               type: string
   *                               example: "http://localhost:3000/login"
   *             examples:
   *               success_response:
   *                 summary: Successful response example
   *                 value:
   *                   error: false
   *                   data:
   *                     message: "Team member added successfully"
   *                     teamMember:
   *                       id: "60d21b4667d0d8992e610c86"
   *                       email: "<EMAIL>"
   *                       name:
   *                         first: "John"
   *                         last: "Doe"
   *                       phone: "1234567890"
   *                       address: "123 Main St, City, Country"
   *                       imageUrl: "https://example.com/profile.jpg"
   *                       isOwner: false
   *                     welcomeEmail:
   *                       sent: false
   *                       message: "Development mode: Welcome email not sent"
   *                       loginDetails:
   *                         email: "<EMAIL>"
   *                         password: "Qwerty@123"
   *                         loginUrl: "http://localhost:3000/login"
   *       400:
   *         description: Bad request - missing required fields
   *       404:
   *         description: User not found
   *       500:
   *         description: Server error
   */
  async addMembers(req, res) {
    try {
      const userId = req.user.id // Get the user ID from the JWT token
      const {
        name, email, phone, address, imageUrl, platformId
      } = req.body // Get the team member data from the request body

      // Find the user in the database
      const user = await User.findOne({ _id: userId })

      // Find the platform where this user is the primary user
      const platform = await Platform.findOne({ _id: platformId, primaryUser: userId })

      if (!platform) {
        return res.status(404).json({
          error: true,
          reason: "Platform not found"
        })
      }

      console.log("platform", platform)
      const teamMembersCount = platform.teamMembers ? platform.teamMembers.length : 0
      // const memberCount = await User.countDocuments({ userType: "TeamMember", _owner: userId })

      if (teamMembersCount === 2) {
        return res.status(400).json({
          error: true,
          reason: "You can only add max 2 team members"
        })
      }

      if (!user) {
        return res.status(404).json({
          error: true,
          reason: "User not found"
        })
      }

      // Validate required fields
      if (!name) {
        return res.status(400).json({
          error: true,
          reason: "Name is required"
        })
      }

      if (!email) {
        return res.status(400).json({
          error: true,
          reason: "Email is required"
        })
      }

      const existingTeamMembersOfPlatform = platform.teamMembers ? platform.teamMembers : []
      const teamMembersForEmail = await User.countDocuments({ email, userType: "TeamMember", _id: { $in: existingTeamMembersOfPlatform } })
      // const emailCount = await User.countDocuments({ email, userType: "TeamMember", _platform: platformId }).exec()
      if (teamMembersForEmail > 0) {
        return res.status(400).json({
          error: true,
          reason: "Team member with given email already exists. Please use a different email."
        })
      }
      if (!phone) {
        return res.status(400).json({
          error: true,
          reason: "Phone number is required"
        })
      }

      if (!address) {
        return res.status(400).json({
          error: true,
          reason: "Address is required"
        })
      }

      // Split name into first and last name
      const [first, ...last] = name.split(" ")
      const lastName = last.join(" ")

      const harcodePass = "Qwerty@123"

      // Create the team member user
      const teamMember = await User.create({
        email,
        phone,
        password: harcodePass,
        name: {
          first,
          last: lastName
        },
        address,
        imageUrl: imageUrl || null,
        isActive: true,
        _owner: userId,
        userType: "TeamMember",
        // _platform: [platformId] // Store as array since we modified the schema
      })

      // Create response object
      const createdMember = {
        id: teamMember._id,
        email: teamMember.email,
        name: teamMember.name,
        phone: teamMember.phone,
        address: teamMember.address,
        imageUrl: teamMember.imageUrl,
        isOwner: false
      }

      // Add the member to the platform's teamMembers array
      if (!platform.teamMembers) {
        platform.teamMembers = []
      }
      platform.teamMembers.push(teamMember._id)

      // Save the platform to the database
      await platform.save()

      // Get platform name
      const platformName = platform.name || "Vewmee White Level"

      // Send welcome email to team member
      const loginUrl = `${process.env.SITE_URL}/login`

      // Send the welcome email
      try {
        await mail("team-welcome", {
          to: teamMember.email,
          subject: "You’ve Been Invited to Help Build a Platform ✨",
          locals: {
            teamMemberName: `${teamMember.name.first} ${teamMember.name.last}`,
            team_email: teamMember.email,
            temporary_password: harcodePass,
            login_link: loginUrl,
            platformName
          }
        })
        console.log(`Team welcome email sent to ${teamMember.email}`)
      } catch (emailError) {
        console.error("Error sending team welcome email:", emailError)
        // Continue execution even if email fails
      }

      return res.status(200).json({
        error: false,
        data: {
          message: "Team member added successfully",
          teamMember: createdMember
        }
      })
    } catch (error) {
      return res.status(500).json({
        error: true,
        reason: error.message
      })
    }
  },

  /**
   * @swagger
   * /user/{id}:
   *   get:
   *     summary: Get user details
   *     tags: [User]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: User's unique ID
   *     responses:
   *       200:
   *         description: User details retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     user:
   *                       type: object
   *                       properties:
   *                         email:
   *                           type: string
   *                           example: "<EMAIL>"
   *                         phone:
   *                           type: string
   *                           example: "00000000000"
   *                         name:
   *                           type: object
   *                           properties:
   *                             first:
   *                               type: string
   *                               example: "John"
   *                             last:
   *                               type: string
   *                               example: "Doe"
   *       404:
   *         description: User not found
   *       500:
   *         description: Server error
   */
  async get(req, res) {
    try {
      const { id } = req.params
      const user = await User.findOne({
        _id: id
      }).populate("platforms")
        .select("-password -forgotpassword -cards")
        .lean()
      if (user === null) throw new Error("No user found for the given id")

      // user = user.toObject()
      user.platform = user.platforms[0]
      delete user.platforms
      return res.status(200).json({
        error: false,
        data: {
          user
        }
      })
    } catch (err) {
      console.log(err)
      return res.status(500).json({
        error: true,
        reason: err.message
      })
    }
  },

  /**
   * @swagger
   * /teamMembers:
   *   post:
   *     summary: Get all team members including owner for a specific platform
   *     tags: [User]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - platformId
   *             properties:
   *               platformId:
   *                 type: string
   *                 description: ID of the platform to get team members for
   *           example:
   *             platformId: "60d21b4667d0d8992e610c85"
   *     responses:
   *       200:
   *         description: List of team members retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     teamMembers:
   *                       type: array
   *                       items:
   *                         type: object
   *                         properties:
   *                           id:
   *                             type: string
   *                             example: "60d21b4667d0d8992e610c85"
   *                           email:
   *                             type: string
   *                             example: "<EMAIL>"
   *                           name:
   *                             type: object
   *                             properties:
   *                               first:
   *                                 type: string
   *                                 example: "John"
   *                               last:
   *                                 type: string
   *                                 example: "Doe"
   *                           phone:
   *                             type: string
   *                             example: "1234567890"
   *                           address:
   *                             type: string
   *                             example: "123 Main St, City, Country"
   *                           imageUrl:
   *                             type: string
   *                             example: "https://example.com/profile.jpg"
   *                           isOwner:
   *                             type: boolean
   *                             description: Indicates if this is an owner (true for owner, false for team members)
   *                       example:
   *                         - id: "60d21b4667d0d8992e610c85"
   *                           email: "<EMAIL>"
   *                           name:
   *                             first: "John"
   *                             last: "Doe"
   *                           phone: "1234567890"
   *                           address: "123 Main St, City, Country"
   *                           imageUrl: "https://example.com/profile.jpg"
   *                           isOwner: true
   *                         - id: "60d21b4667d0d8992e610c86"
   *                           email: "<EMAIL>"
   *                           name:
   *                             first: "Jane"
   *                             last: "Smith"
   *                           phone: "0987654321"
   *                           address: "456 Oak St, City, Country"
   *                           imageUrl: "https://example.com/profile2.jpg"
   *                           isOwner: false
   *       500:
   *         description: Server error
   */
  async getTeamMembers(req, res) {
    try {
      const { platformId } = req.body
      const userId = req.user.id // Get the user ID from the JWT token

      // Find the platform where this user is the primary user
      const platform = await Platform.findOne({ _id: platformId, primaryUser: userId })
        .populate("teamMembers", "-password -forgotpassword")
        .populate("primaryUser", "-password -forgotpassword -cards").exec()

      if (!platform) {
        return res.status(404).json({
          error: true,
          reason: "Platform not found"
        })
      }

      console.log("platform", platform)
      // const teamMembers = await TeamMember.find({ _platform: platformId })
      //   .select("-password -forgotpassword -cards")
      //   .lean()
      const teamMembers = platform.teamMembers
      // const owner = await Owner.findOne({ _id: userId })
      //   .select("-password -forgotpassword -cards")
      //   .exec()
      // console.log(owner)
      const owner = platform.primaryUser
      // Format the team members
      const formattedTeamMembers = teamMembers.map((member) => ({
        id: member._id,
        name: member.name,
        email: member.email,
        phone: member.phone,
        address: member.address,
        imageUrl: member.imageUrl,
        isOwner: false // Always false for team members
      }))
      // Add the owner to the beginning of the array with isOwner: true
      formattedTeamMembers.unshift({
        id: owner._id,
        name: owner.name,
        email: owner.email,
        phone: owner.phone,
        address: owner.address,
        imageUrl: owner.imageUrl,
        isOwner: true // Always true for the owner
      })
      return res.status(200).json({
        error: false,
        data: {
          teamMembers: formattedTeamMembers
        }
      })
    } catch (error) {
      return res.status(500).json({
        error: true,
        reason: error.message
      })
    }
  },

  /**
   * @swagger
   * /teamMember/{id}:
   *   put:
   *     summary: Update a team member or owner profile
   *     description: |
   *       Updates a team member's profile or the owner's own profile.
   *       - If updating a team member, all fields can be updated.
   *       - If updating the owner's profile (self), only phone, address, and imageUrl can be updated.
   *     tags: [User]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Team member's ID or owner's own ID
   *     requestBody:
   *       required: false
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               name:
   *                 type: string
   *                 description: Full name of the team member (will be split into first and last name). Ignored for owner self-updates.
   *               email:
   *                 type: string
   *                 description: Email of the team member. Ignored for owner self-updates.
   *               phone:
   *                 type: string
   *                 description: Phone number of the team member or owner
   *               address:
   *                 type: string
   *                 description: Address of the team member or owner
   *               imageUrl:
   *                 type: string
   *                 description: Profile image URL of the team member or owner
   *           example:
   *             name: "John Doe"
   *             email: "<EMAIL>"
   *             phone: "1234567890"
   *             address: "123 Main St, City, Country"
   *             imageUrl: "https://example.com/profile.jpg"
   *     responses:
   *       200:
   *         description: Team member or owner profile updated successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     message:
   *                       type: string
   *                       example: "Team member updated successfully"
   *                     teamMember:
   *                       type: object
   *                       properties:
   *                         id:
   *                           type: string
   *                           example: "60d21b4667d0d8992e610c85"
   *                         email:
   *                           type: string
   *                           example: "<EMAIL>"
   *                         name:
   *                           type: object
   *                           properties:
   *                             first:
   *                               type: string
   *                               example: "John"
   *                             last:
   *                               type: string
   *                               example: "Doe"
   *                         phone:
   *                           type: string
   *                           example: "1234567890"
   *                         address:
   *                           type: string
   *                           example: "123 Main St, City, Country"
   *                         imageUrl:
   *                           type: string
   *                           example: "https://example.com/profile.jpg"
   *                         isOwner:
   *                           type: boolean
   *                           example: false
   *                           description: Indicates if this is the owner (true) or a team member (false)
   *       403:
   *         description: Not authorized to update this team member
   *       404:
   *         description: Team member not found
   *       500:
   *         description: Server error
   */
  async update(req, res) {
    try {
      const ownerId = req.user.id // Get the owner ID from the JWT token
      const memberId = req.params.id // Get the team member ID from the request parameters
      const {
        name, email, phone, address, imageUrl
      } = req.body // Get the team member data from the request body

      // Find the team member in the database
      const teamMember = await User.findOne({ _id: memberId })

      if (!teamMember) {
        return res.status(404).json({
          error: true,
          reason: "Team member not found"
        })
      }

      // Check if the user is trying to update their own profile
      const isUpdatingSelf = memberId === ownerId

      // If not updating self, check if the logged-in user is the owner of this team member
      if (!isUpdatingSelf && teamMember._owner.toString() !== ownerId) {
        return res.status(403).json({
          error: true,
          reason: "You are not authorized to update this team member"
        })
      }

      // If updating self (owner), only allow updating phone, address, and imageUrl
      if (isUpdatingSelf) {
        if (name || email) {
          return res.status(400).json({
            error: true,
            reason: "Name and email cannot be updated for owner self-update"
          })
        }

        // For owners, only allow updating phone, address, and imageUrl
        if (phone) teamMember.phone = phone
        if (address) teamMember.address = address
        if (imageUrl) teamMember.imageUrl = imageUrl
      } else {
        // For team members, allow updating all fields
        if (name) {
          const [first, ...last] = name.split(" ")
          const lastName = last.join(" ")
          teamMember.name = {
            first,
            last: lastName
          }
        }
        if (email) teamMember.email = email
        if (phone) teamMember.phone = phone
        if (address) teamMember.address = address
        if (imageUrl) teamMember.imageUrl = imageUrl
      }

      // Save the team member to the database
      await teamMember.save()

      // Format the response
      const updatedMember = {
        id: teamMember._id,
        name: teamMember.name,
        email: teamMember.email,
        phone: teamMember.phone,
        address: teamMember.address,
        imageUrl: teamMember.imageUrl,
        isOwner: teamMember.userType === "Owner" // Add isOwner flag based on userType
      }

      const message = isUpdatingSelf
        ? "Owner profile updated successfully"
        : "Team member updated successfully"

      return res.status(200).json({
        error: false,
        data: {
          message,
          teamMember: updatedMember
        }
      })
    } catch (error) {
      return res.status(500).json({
        error: true,
        reason: error.message
      })
    }
  },

  /**
   * @swagger
   * /teamMember/delete/{id}:
   *   put:
   *     summary: Delete a team member
   *     tags: [User]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Team member's ID
   *     responses:
   *       200:
   *         description: Team member deleted successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     message:
   *                       type: string
   *                       example: "Team member deleted successfully"
   *       403:
   *         description: Not authorized to delete this team member
   *       404:
   *         description: Team member not found
   *       500:
   *         description: Server error
   */
  async delete(req, res) {
    try {
      const ownerId = req.user.id // Get the owner ID from the JWT token
      const memberId = req.params.id // Get the team member ID from the request parameters

      // Find the team member in the database
      const teamMember = await User.findOne({ _id: memberId })

      if (!teamMember) {
        return res.status(404).json({
          error: true,
          reason: "Team member not found"
        })
      }

      // Check if the logged-in user is the owner of this team member
      if (teamMember._owner.toString() !== ownerId) {
        return res.status(403).json({
          error: true,
          reason: "You are not authorized to delete this team member"
        })
      }

      // Find the platform and remove the team member from its teamMembers array
      const platform = await Platform.findOne({ primaryUser: ownerId })
      if (platform) {
        platform.teamMembers = platform.teamMembers.filter((id) => id.toString() !== memberId)
        await platform.save()
      }

      // Delete the team member from the database
      await User.deleteOne({ _id: memberId })

      return res.status(200).json({
        error: false,
        data: {
          message: "Team member deleted successfully"
        }
      })
    } catch (error) {
      return res.status(500).json({
        error: true,
        reason: error.message
      })
    }
  },

  /**
   * @swagger
   * /updateProfile:
   *   put:
   *     summary: Update user profile
   *     description: Updates the authenticated user's profile information
   *     tags: [User]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               name:
   *                 type: string
   *                 description: Full name of the user (will be split into first and last name)
   *               email:
   *                 type: string
   *                 description: Email of the user
   *               phone:
   *                 type: string
   *                 description: Phone number of the user
   *               address:
   *                 type: string
   *                 description: Address of the user
   *               imageUrl:
   *                 type: string
   *                 description: Profile image URL of the user
   *               gender:
   *                 type: string
   *                 enum: [Male, Female, Other]
   *                 description: Gender of the user
   *               launchStatus:
   *                 type: Number
   *                 description: Current launch status of the user's platform
   *           example:
   *             name: "John Doe"
   *             email: "<EMAIL>"
   *             phone: "1234567890"
   *             address: "123 Main St, City, Country"
   *             imageUrl: "https://example.com/profile.jpg"
   *             gender: "Male"
   *             launchStatus: 1
   *     responses:
   *       200:
   *         description: Profile updated successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     message:
   *                       type: string
   *                       example: "Profile updated successfully"
   *                     user:
   *                       type: object
   *                       properties:
   *                         id:
   *                           type: string
   *                           example: "60d21b4667d0d8992e610c85"
   *                         email:
   *                           type: string
   *                           example: "<EMAIL>"
   *                         name:
   *                           type: object
   *                           properties:
   *                             first:
   *                               type: string
   *                               example: "John"
   *                             last:
   *                               type: string
   *                               example: "Doe"
   *                         phone:
   *                           type: string
   *                           example: "1234567890"
   *                         address:
   *                           type: string
   *                           example: "123 Main St, City, Country"
   *                         imageUrl:
   *                           type: string
   *                           example: "https://example.com/profile.jpg"
   *                         gender:
   *                           type: string
   *                           example: "Male"
   *                         launchStatus:
   *                           type: Number
   *                           example: 1
   *                           description: Current launch status of the user's platform
   *       404:
   *         description: User not found
   *       500:
   *         description: Server error
   */
  async updateProfile(req, res) {
    try {
      const userId = req.user.id // Get the user ID from the JWT token
      const {
        name, email, phone, address, imageUrl, gender
      } = req.body // Get the user data from the request body

      // Find the user in the database
      const user = await User.findOne({ _id: userId })

      if (!user) {
        return res.status(404).json({
          error: true,
          reason: "User not found"
        })
      }

      // Update the user's details if provided
      if (name) {
        const [first, ...last] = name.split(" ")
        const lastName = last.join(" ")
        user.name = {
          first,
          last: lastName
        }
      }

      // If email is being changed, check if it's already in use
      if (email && email !== user.email) {
        const existingUser = await User.findOne({ email, _id: { $ne: userId } })
        if (existingUser) {
          return res.status(400).json({
            error: true,
            reason: "Email is already in use"
          })
        }
        user.email = email
        // Reset email verification if email is changed
        user.isEmailVerified = false
      }

      if (phone) user.phone = phone
      if (address) user.address = address
      if (imageUrl) user.imageUrl = imageUrl
      if (gender && ["Male", "Female", "Other"].includes(gender)) user.gender = gender

      // Save the user to the database
      await user.save()

      // Get platforms data if they exist
      let platformsData = []
      if (user.platforms && user.platforms.length > 0) {
        // Find platforms where this user is the primary user
        const platforms = await Platform.find({
          primaryUser: userId,
          _id: { $in: user.platforms }
        }).lean()

        if (platforms && platforms.length > 0) {
          platformsData = platforms.map((platform) => ({
            id: platform._id,
            name: platform.name,
            launchStatus: platform.launchStatus
          }))
        }
      }

      // Format the response
      const updatedUser = {
        id: user._id,
        name: user.name,
        email: user.email,
        phone: user.phone,
        address: user.address,
        imageUrl: user.imageUrl,
        gender: user.gender,
        isEmailVerified: user.isEmailVerified,
        platforms: platformsData
      }

      return res.status(200).json({
        error: false,
        data: {
          message: "Profile updated successfully",
          user: updatedUser
        }
      })
    } catch (error) {
      return res.status(500).json({
        error: true,
        reason: error.message
      })
    }
  },

  /**
   * @swagger
   * /awstempcreds:
   *   get:
   *     summary: Get temporary AWS credentials
   *     description: Generates temporary AWS credentials for client-side S3 operations
   *     tags: [User]
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: AWS credentials retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     S3BucketName:
   *                       type: string
   *                       description: AWS S3 bucket name
   *                       example: "truefanz-dev"
   *                     S3Region:
   *                       type: string
   *                       description: AWS S3 region
   *                       example: "us-east-2"
   *                     AccessKeyId:
   *                       type: string
   *                       description: AWS access key ID
   *                       example: "********************"
   *                     SecretAccessKey:
   *                       type: string
   *                       description: AWS secret access key
   *                       example: "uWmhr5noIGWwbgAdi/iaCZBKrBC677KBGapZ09I1"
   *                     SessionToken:
   *                       type: string
   *                       description: AWS session token
   *                       example: "FwoGZXIvYXdzEL///////////wEaDConorW2JecB3y86yyKCAeOh7rSEmyGs/esvZjhHAyj471090veVFNp5anzhuLVZufGX/S8xFCuipBQefJNT1Gj3gwETxZ6dm7+qnPlnBZk0T9/P4VU/rWXWkmK26wgTcXIS+3QLhPlbPFXKiHNjcFYIq9F2pufFMg1hTox1aUL19tixEW2nHLYmwApyLphLUygo4u7rwAYyKOUUNuQ39LWWXwKFuuO5jjTJLSnDTajDA1AotYsrLqlh8fc64yNjMKM="
   *                     Expiration:
   *                       type: string
   *                       format: date-time
   *                       description: Expiration time of the temporary credentials
   *                       example: "2025-05-07T18:02:10.000Z"
   *       500:
   *         description: Server error
   */
  async getAwsKey(req, res) {
    try {
      const sts = new STS({ accessKeyId: process.env.AWS_ACCESS_KEY, secretAccessKey: process.env.AWS_SECRET_KEY })
      const { Credentials } = await sts.getSessionToken().promise()

      // CORS headers
      res.setHeader("Access-Control-Allow-Origin", "*")
      res.setHeader("Access-Control-Request-Method", "*")
      res.setHeader("Access-Control-Allow-Methods", "OPTIONS, GET, POST, PUT")
      res.setHeader("Access-Control-Allow-Headers", "authorization, origin, x-requested-with, x-http-method-override, content-type, Overwrite, Destination, Depth, User-Agent, Translate, Range, Content-Range, Timeout, X-File-Size, If-Modified-Since, X-File-Name, Cache-Control, Location, Lock-Token")

      if (req.method === "OPTIONS") {
        res.writeHead(200)
        return res.end()
      }

      return res.status(200).json({
        error: false,
        data: {
          S3BucketName: process.env.AWS_BUCKET_FOR_IMAGE,
          S3Region: process.env.AWS_BUCKET_REGION,
          ...Credentials
        }
      })
    } catch (err) {
      console.log("==> ERR generating AWS creds: ", err)
      return res.status(500).json({ error: true, reason: err.message })
    }
  },
}
