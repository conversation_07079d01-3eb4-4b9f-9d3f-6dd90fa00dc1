/* eslint-disable max-len */
const { User, Subscription, Payment, Platform } = require("../../models")
const stripeService = require("../../lib/stripe")

module.exports = {
  /**
   * @swagger
   * /subscription:
   *   get:
   *     summary: Get subscription details for the user's platform
   *     description: Retrieves subscription details for the platform associated with the authenticated user. For owners, gets the subscription of their first platform. For team members, gets the subscription of the platform they belong to.
   *     tags: [Subscription]
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: Subscription details retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     subscription:
   *                       type: object
   *                       nullable: true
   *                       properties:
   *                         _id:
   *                           type: string
   *                           example: "60d21b4667d0d8992e610c85"
   *                         user:
   *                           type: string
   *                           example: "60d21b4667d0d8992e610c85"
   *                           description: ID of the user who owns the subscription (platform owner)
   *                         status:
   *                           type: string
   *                           example: "active"
   *                           enum: ["inactive", "active", "expired", "cancelled", "past_due", "unpaid"]
   *                         setupFeePaid:
   *                           type: boolean
   *                           example: true
   *                         startDate:
   *                           type: string
   *                           format: date-time
   *                           example: "2023-01-01T00:00:00.000Z"
   *                         endDate:
   *                           type: string
   *                           format: date-time
   *                           example: "2023-04-01T00:00:00.000Z"
   *                         currentPeriodStart:
   *                           type: string
   *                           format: date-time
   *                           example: "2023-01-01T00:00:00.000Z"
   *                         currentPeriodEnd:
   *                           type: string
   *                           format: date-time
   *                           example: "2023-04-01T00:00:00.000Z"
   *                         model:
   *                           type: string
   *                           example: "flat-fee"
   *                           enum: ["revenue-share", "flat-fee", "setup-fee"]
   *                         stripeId:
   *                           type: string
   *                           example: "cus_123456789"
   *                           description: Stripe customer ID
   *                         defaultPaymentMethodId:
   *                           type: string
   *                           example: "pm_123456789"
   *                           description: Default payment method ID
   *                         _platform:
   *                           type: string
   *                           example: "60d21b4667d0d8992e610c85"
   *                           description: ID of the platform this subscription belongs to
   *                     launchStatus:
   *                       type: number
   *                       example: 1
   *                       description: Current launch status of the platform
   *       404:
   *         description: User not found
   *       500:
   *         description: Server error
   */
  async get(req, res) {
    try {
      const userId = req.user.id

      // First, find the user
      const user = await User.findOne({ _id: userId })
      if (!user) {
        return res.status(404).json({
          error: true,
          reason: "User not found"
        })
      }

      // Find the platform associated with the user
      let platform

      if (user.userType === "Owner" && user.platforms && user.platforms.length > 0) {
        // For owners, get the first platform
        const [firstPlatformId] = user.platforms
        platform = await Platform.findOne({ _id: firstPlatformId }).lean()
      } else if (user.userType === "TeamMember" && user._owner) {
        // For team members, find the platform where they are a team member
        platform = await Platform.findOne({
          teamMembers: userId
        }).lean()
      }

      if (!platform) {
        return res.json({
          error: false,
          data: {
            subscription: null,
            launchStatus: null
          }
        })
      }

      // Get subscription details if exists
      let subscription = null
      if (platform.subscription) {
        subscription = await Subscription.findOne({ _id: platform.subscription }).lean()
      } else {
        // If platform doesn't have a subscription reference, try to find by platform ID
        subscription = await Subscription.findOne({ _platform: platform._id }).lean()
      }

      return res.json({
        error: false,
        data: {
          subscription,
          launchStatus: platform.launchStatus
        }
      })
    } catch (error) {
      return res.status(500).json({
        error: true,
        reason: error.message
      })
    }
  },

}
