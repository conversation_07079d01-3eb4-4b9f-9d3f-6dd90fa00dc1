/* eslint-disable max-len */
const axios = require("axios")
const stripeService = require("../../lib/stripe")
const {
  User, Payment, Subscription, Owner, Platform
} = require("../../models")
const paymentProcessor = require("../../lib/payment-processor")
const agenda = require("../../agenda")
module.exports = {
  /**
   * @swagger
   * /payment/card:
   *   post:
   *     summary: Add a new card to user's Stripe account
   *     tags: [Payment]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - cardToken
   *             properties:
   *               cardToken:
   *                 type: string
   *                 description: Stripe card token generated from the generateToken endpoint
   *                 example: "tok_1234567890"
   *     responses:
   *       200:
   *         description: Card added successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     card:
   *                       type: object
   *                       properties:
   *                         id:
   *                           type: string
   *                           example: "card_1234567890"
   *                         last4:
   *                           type: string
   *                           example: "4242"
   *                         brand:
   *                           type: string
   *                           example: "visa"
   *                         expMonth:
   *                           type: integer
   *                           example: 12
   *                         expYear:
   *                           type: integer
   *                           example: 2025
   *                         name:
   *                           type: string
   *                           example: ""
   *                         isDefault:
   *                           type: boolean
   *                           example: true
   *       400:
   *         description: Bad request - missing card token or user not found
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 reason:
   *                   type: string
   *                   example: "Card token is required"
   *       500:
   *         description: Server error
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 reason:
   *                   type: string
   *                   example: "An error occurred while adding the card"
   */
  async addCard(req, res) {
    try {
      const { cardToken } = req.body
      const userId = req.user._id

      if (!cardToken) {
        return res.status(400).json({
          error: true,
          reason: "Card token is required"
        })
      }

      const user = await User.findById(userId)
      if (!user || !user.stripeId) {
        return res.status(400).json({
          error: true,
          reason: "User not found or Stripe account not set up"
        })
      }

      const card = await stripeService.addCard(user.stripeId, cardToken)

      // Store card information in user schema
      const cardData = {
        id: card.id,
        last4: card.last4,
        brand: card.brand,
        expMonth: card.exp_month,
        expYear: card.exp_year,
        name: card.name || "",
        isDefault: false // New cards are not default by default
      }

      // If this is the first card, make it default
      if (!user.cards || user.cards.length === 0) {
        cardData.isDefault = true
      }

      // Add card to user's cards array
      await User.findByIdAndUpdate(userId, {
        $push: { cards: cardData }
      })

      return res.json({
        error: false,
        data: {
          card: cardData
        }
      })
    } catch (err) {
      return res.status(500).json({
        error: true,
        reason: err.message
      })
    }
  },

  /**
   * @swagger
   * /payment/cards:
   *   get:
   *     summary: Fetch all cards for a user
   *     tags: [Payment]
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: Cards retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     cards:
   *                       type: array
   *                       items:
   *                         type: object
   *                         properties:
   *                           id:
   *                             type: string
   *                             example: "card_1234567890"
   *                           last4:
   *                             type: string
   *                             example: "4242"
   *                           brand:
   *                             type: string
   *                             example: "visa"
   *                           expMonth:
   *                             type: integer
   *                             example: 12
   *                           expYear:
   *                             type: integer
   *                             example: 2025
   *                           name:
   *                             type: string
   *                             example: ""
   *                           isDefault:
   *                             type: boolean
   *                             example: true
   *       400:
   *         description: Bad request - user not found
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 reason:
   *                   type: string
   *                   example: "User not found"
   */
  async fetchCards(req, res) {
    try {
      const userId = req.user._id

      const user = await User.findById(userId)
      if (!user) {
        return res.status(400).json({
          error: true,
          reason: "User not found"
        })
      }

      const cards = await stripeService.fetchCard(user.stripeId)
      // Return cards from user schema
      return res.json({
        error: false,
        data: {
          cards: cards || []
        }
      })
    } catch (error) {
      console.log("error in fetchCards", error)
      return res.status(400).json({
        error: true,
        reason: error.message
      })
    }
  },

  /**
   * @swagger
   * /payment/card/default:
   *   post:
   *     summary: Add a new card and set it as default
   *     tags: [Payment]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - token
   *             properties:
   *               token:
   *                 type: string
   *                 description: Stripe card token
   *                 example: "tok_1234567890"
   *     responses:
   *       200:
   *         description: Default card set successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     card:
   *                       type: object
   *                       properties:
   *                         id:
   *                           type: string
   *                           example: "card_1234567890"
   *                         last4:
   *                           type: string
   *                           example: "4242"
   *                         brand:
   *                           type: string
   *                           example: "visa"
   *                         exp_month:
   *                           type: integer
   *                           example: 12
   *                         exp_year:
   *                           type: integer
   *                           example: 2025
   *                         is_default:
   *                           type: boolean
   *                           example: true
   *       400:
   *         description: Bad request - user not found
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 reason:
   *                   type: string
   *                   example: "User not found"
   */
  async addDefaultCard(req, res) {
    try {
      const { stripeId, id } = req.user
      const { token } = req.body
      const user = await User.findOne({ _id: id }).exec()

      if (user === null) {
        // throw new Error("fan not found")
        return res.status(400).json({
          error: true,
          reason: "User not found"
        })
      }

      const card = await stripeService.addCard(stripeId, token)
      const defaultCard = await stripeService.setDefaultCard(stripeId, card.id)

      return res.json({
        error: false,
        data: {
          card: defaultCard
        }
      })
    } catch (error) {
      console.log("error in addDefaultCard", error)
      return res.status(400).json({
        error: true,
        reason: error.message
      })
    }
  },

  async confirmPayment(req, res) {
    try {
      const {
        paymentIntentId,
        paymentMethodId,
        clientSecret,
        expectedPaymentMethodType = "card",
        useStripeSdk = true,
        stripeVersion = "2020-08-27",
        stripeKey = process.env.STRIPE_PUBLISHABLE_KEY
      } = req.body

      const userId = req.user._id

      if (!paymentIntentId || !paymentMethodId) {
        return res.status(400).json({
          error: true,
          reason: "Payment intent ID and payment method ID are required"
        })
      }

      const user = await User.findById(userId)
      if (!user || !user.stripeId) {
        return res.status(400).json({
          error: true,
          reason: "User not found or Stripe account not set up"
        })
      }

      // Using axios imported at the top of the file

      // Prepare form data
      const formData = new URLSearchParams()
      formData.append("payment_method", paymentMethodId)
      formData.append("expected_payment_method_type", expectedPaymentMethodType)
      formData.append("use_stripe_sdk", useStripeSdk)
      formData.append("key", stripeKey)
      formData.append("_stripe_version", stripeVersion)

      // Add client_secret if provided
      if (clientSecret) {
        formData.append("client_secret", clientSecret)
      }

      // Make direct API call to Stripe
      const response = await axios({
        method: "post",
        url: `https://api.stripe.com/v1/payment_intents/${paymentIntentId}/confirm`,
        data: formData,
        headers: {
          "Content-Type": "application/x-www-form-urlencoded"
        }
      })

      const paymentIntent = response.data

      // Update payment record if payment was successful
      if (paymentIntent.status === "succeeded") {
        await Payment.updateOne(
          { paymentIntentId: paymentIntent.id },
          { $set: { status: "completed" } }
        )
      }

      return res.json({
        error: false,
        data: {
          paymentIntent: {
            id: paymentIntent.id,
            status: paymentIntent.status,
            amount: paymentIntent.amount,
            currency: paymentIntent.currency,
            client_secret: paymentIntent.client_secret
          }
        }
      })
    } catch (err) {
      console.error("Payment confirmation error:", err.response && err.response.data ? err.response.data : err.message)
      return res.status(500).json({
        error: true,
        reason: err.response && err.response.data && err.response.data.error ? err.response.data.error.message : err.message
      })
    }
  },

  /**
   * @swagger
   * /payment/setup-fee:
   *   post:
   *     summary: Pay setup fee and start subscription
   *     tags: [Payment]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - platformId
   *             properties:
   *               platformId:
   *                 type: string
   *                 description: ID of the platform for which the setup fee is being paid
   *                 example: "60d21b4667d0d8992e610c85"
   *     responses:
   *       200:
   *         description: Setup fee payment initiated successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     launchStatus:
   *                       type: string
   *                       example: "subscription"
   *                     payment:
   *                       type: object
   *                       properties:
   *                         _id:
   *                           type: string
   *                           example: "60d21b4667d0d8992e610c85"
   *                         amount:
   *                           type: number
   *                           example: 45000
   *                         status:
   *                           type: string
   *                           example: "pending"
   *                         paymentIntentId:
   *                           type: string
   *                           example: "pi_1234567890"
   *                         paymentFor:
   *                           type: string
   *                           example: "subscription-setup"
   *                     defaultCard:
   *                       type: object
   *                       properties:
   *                         id:
   *                           type: string
   *                           example: "card_1234567890"
   *                         last4:
   *                           type: string
   *                           example: "4242"
   *                         brand:
   *                           type: string
   *                           example: "visa"
   *       400:
   *         description: Bad request - setup fee already paid or no default payment method
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 reason:
   *                   type: string
   *                   example: "Setup fee already paid"
   *       404:
   *         description: User not found
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 reason:
   *                   type: string
   *                   example: "User not found"
   *       500:
   *         description: Server error
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 reason:
   *                   type: string
   *                   example: "An error occurred while processing your payment"
   */
  async paySetupFee(req, res) {
    try {
      const userId = req.user.id
      const amount = process.env.SETUP_FEE_AMOUNT
      const { platformId } = req.body
      // Get user
      const user = await User.findOne({ _id: userId }).exec()

      if (!user) {
        return res.status(404).json({
          error: true,
          reason: "User not found"
        })
      }

      if (!platformId) {
        return res.status(404).json({
          error: true,
          reason: "Mandatory field `platformId` not found"
        })
      }
      // Get existing subscription if any
      let existingSubscription = null
      if (user.subscription) {
        existingSubscription = await Subscription.findOne({ _platform: platformId, user: userId }).lean()
      }

      // Check if setup fee already paid
      if (existingSubscription && existingSubscription.setupFeePaid) {
        return res.status(400).json({
          error: true,
          reason: "Setup fee already paid"
        })
      }

      // Create or get Stripe customer
      let stripeId = user.stripeId && user.stripeId !== "" ? user.stripeId : null

      if (!stripeId) {
        const customer = await stripeService.createAccount(user.email)
        stripeId = customer.id
        user.stripeId = stripeId
      }

      // Fetch default payment method
      const defaultCard = await stripeService.fetchDefaultCard(stripeId)
      if (!defaultCard) {
        return res.status(400).json({
          error: true,
          reason: "No default payment method found. Please add a card."
        })
      }

      // const paymentMethodId = defaultCard.id
      console.log("Payment model:", Payment)
      // Process setup fee payment
      const description = `Setup fee for ${user.email}`
      const paymentIntent = await stripeService.createPaymentIntent(
        stripeId,
        amount,
        description
      )
      console.log("paymentIntent", paymentIntent)
      // Create payment record
      const payment = await Payment.create({
        _user: userId,
        amount, // $450 setup fee
        platformFee: 0,
        processingFee: 0,
        totalDeducted: 0,
        netPayout: 0,
        status: "pending",
        paymentIntentId: paymentIntent.id,
        // paymentMethod: paymentIntent.payment_method,
        paymentFor: "subscription-setup",
        currency: "USD",
        _platform: platformId,
        _subscription: existingSubscription ? existingSubscription._id : null
      })

      await user.save()

      return res.json({
        error: false,
        data: {
          launchStatus: user.launchStatus,
          payment,
          defaultCard,
          clientSecret: paymentIntent.client_secret
        }
      })
    } catch (error) {
      console.error("Setup fee payment error:", error)
      return res.status(500).json({
        error: true,
        reason: error.message || "An error occurred while processing your payment"
      })
    }
  },

  async generateToken(req, res) {
    try {
      const {
        cardNumber, expiryMonth, expiryYear, cvc
      } = req.body
      if (cardNumber === undefined) {
        // throw new Error("invalid cardNumber")
        return res.status(400).json({
          error: true,
          reason: "CARDNUMBER_INVALID_UNDEFINED"
        })
      }
      if (expiryMonth === undefined) {
        // throw new Error("invalid expiryMonth")
        return res.status(400).json({
          error: true,
          reason: "EXPIRYMONTH_INVALID_UNDEFINED[req.headers.language"
        })
      }
      if (expiryYear === undefined) {
        // throw new Error("invalid expiryYear")
        return res.status(400).json({
          error: true,
          reason: "EXPIRYYEAR_INVALID_UNDEFINED"
        })
      }
      if (cvc === undefined) {
        // throw new Error("invalid cvc")
        return res.status(400).json({
          error: true,
          reason: "CVC_INVALID_UNDEFINED"
        })
      }

      const payload = {
        card: {
          number: cardNumber,
          exp_month: expiryMonth,
          exp_year: expiryYear,
          cvc
        }
      }

      const token = await stripeService.createAccountToken(payload)

      return res.json({
        error: false,
        token
      })
    } catch (error) {
      return res.status(400).json({
        error: true,
        reason: error.message
      })
    }
  },
  /**
   * @swagger
   * /make/card/default:
   *   post:
   *     summary: Set a card as the default payment method
   *     tags: [Payment]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - cardId
   *             properties:
   *               cardId:
   *                 type: string
   *                 description: ID of the card to set as default
   *                 example: "card_1234567890"
   *     responses:
   *       200:
   *         description: Default card updated successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     message:
   *                       type: string
   *                       example: "Default card updated successfully"
   *       400:
   *         description: Bad request - missing card ID or user not found
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 reason:
   *                   type: string
   *                   example: "Card ID is required"
   *       500:
   *         description: Server error
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 reason:
   *                   type: string
   *                   example: "An error occurred while updating the default card"
   */
  async setDefaultCard(req, res) {
    try {
      const { cardId } = req.body
      const userId = req.user._id

      if (!cardId) {
        return res.status(400).json({
          error: true,
          reason: "Card ID is required"
        })
      }

      const user = await User.findById(userId)
      if (!user) {
        return res.status(400).json({
          error: true,
          reason: "User not found"
        })
      }

      // Update all cards to set isDefault to false
      // await User.findByIdAndUpdate(userId, {
      //   $set: { "cards.$[].isDefault": false }
      // })

      // // Set the specified card as default
      // await User.findByIdAndUpdate(userId, {
      //   $set: { "cards.$[card].isDefault": true }
      // }, {
      //   arrayFilters: [{ "card.id": cardId }]
      // })

      // Update default payment method in Stripe
      if (user.stripeId) {
        await stripeService.setDefaultCard(user.stripeId, cardId)
      }

      return res.json({
        error: false,
        data: {
          message: "Default card updated successfully"
        }
      })
    } catch (error) {
      console.log("error in setDefaultCard", error)
      return res.status(400).json({
        error: true,
        reason: error.message
      })
    }
  },
  /**
   * @swagger
   * /payment/card/delete:
   *   post:
   *     summary: Delete a payment card
   *     tags: [Payment]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - cardId
   *             properties:
   *               cardId:
   *                 type: string
   *                 description: ID of the card to delete
   *                 example: "card_1234567890"
   *     responses:
   *       200:
   *         description: Card deleted successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     message:
   *                       type: string
   *                       example: "Card deleted successfully"
   *       400:
   *         description: Bad request - missing card ID or user not found
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 reason:
   *                   type: string
   *                   example: "Card ID is required"
   *       500:
   *         description: Server error
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 reason:
   *                   type: string
   *                   example: "An error occurred while deleting the card"
   */
  async deleteCard(req, res) {
    try {
      const { cardId } = req.body
      const userId = req.user._id

      if (!cardId) {
        return res.status(400).json({
          error: true,
          reason: "Card ID is required"
        })
      }

      const user = await User.findById(userId)
      if (!user) {
        return res.status(400).json({
          error: true,
          reason: "User not found"
        })
      }

      // Remove card from Stripe if user has a Stripe account
      if (user.stripeId) {
        await stripeService.deleteCard(user.stripeId, cardId)
      }

      // Remove card from user schema
      await User.findByIdAndUpdate(userId, {
        $pull: { cards: { id: cardId } }
      })

      return res.json({
        error: false,
        data: {
          message: "Card deleted successfully"
        }
      })
    } catch (error) {
      console.log("error in deleteCard", error)
      return res.status(400).json({
        error: true,
        reason: error.message
      })
    }
  },

  /**
   * @swagger
   * /test/fetch/balance:
   *   post:
   *     summary: Test endpoint to fetch vendor Stripe account balance and earnings
   *     tags: [Testing]
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: Successfully retrieved vendor Stripe earnings data
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     earnings:
   *                       type: object
   *                       properties:
   *                         balance:
   *                           type: object
   *                           properties:
   *                             available:
   *                               type: number
   *                               example: 1000.00
   *                             pending:
   *                               type: number
   *                               example: 500.00
   *                         earnings:
   *                           type: object
   *                           properties:
   *                             total:
   *                               type: number
   *                               example: 5000.00
   *                             currency:
   *                               type: string
   *                               example: "usd"
   *                             period:
   *                               type: object
   *                               properties:
   *                                 start:
   *                                   type: string
   *                                   format: date-time
   *                                 end:
   *                                   type: string
   *                                   format: date-time
   *                         transactions:
   *                           type: array
   *                           items:
   *                             type: object
   *       400:
   *         description: Error fetching balance
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 reason:
   *                   type: string
   *                   example: "Error fetching vendor Stripe earnings"
   *       404:
   *         description: Vendor not found
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 reason:
   *                   type: string
   *                   example: "Vendor not found. Authenticated user is not a vendor."
   */
  async testFetchBalance(req, res) {
    try {
      // Get the authenticated user
      const owner = await Owner.findOne({ _id: req.user.id }).exec()

      if (!owner || !owner.vendorStripeId) {
        return res.status(404).json({
          error: true,
          reason: "Vendor not found. Authenticated user is not a vendor."
        })
      }

      const earnings = await stripeService.fetchVendorEarnings(owner.vendorStripeId)
      return res.json({
        error: false,
        data: {
          earnings
        }
      })
    } catch (error) {
      console.log("error in testFetchBalance", error)
      return res.status(400).json({
        error: true,
        reason: error.message
      })
    }
  },

  /**
   * @swagger
   * /user/fresh/kyc:
   *   get:
   *     summary: Generate a fresh KYC link for influencer Stripe verification
   *     description: Generates a new Stripe account onboarding link for influencers to complete their KYC verification process
   *     tags: [Payment]
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: Successfully generated KYC link
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 kycLink:
   *                   type: object
   *                   properties:
   *                     object:
   *                       type: string
   *                       example: "account_link"
   *                     created:
   *                       type: integer
   *                       example: **********
   *                     expires_at:
   *                       type: integer
   *                       example: **********
   *                     url:
   *                       type: string
   *                       example: "https://connect.stripe.com/setup/s/..."
   *       400:
   *         description: Bad request - owner not found or missing vendor Stripe ID
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 reason:
   *                   type: string
   *                   example: "Owner not found or missing vendor Stripe ID"
   *       500:
   *         description: Server error or KYC already completed
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 reason:
   *                   type: string
   *                   example: "Already done Kyc"
   */
  async getInfluencerFreshKycLink(req, res) {
    try {
      const owner = await Owner.findOne({ _id: req.user.id }).exec()

      if (owner === null) {
        return res.status(400).json({
          error: true,
          reason: ""
        })
      }

      if (owner.vendorStripeId === undefined) {
        return res.status(400).json({
          error: true,
          reason: owner.vendorStripeId
        })
      }

      const resp = await stripeService.retrieveVendorAccount(owner.vendorStripeId)

      const needToCheckData = {
        transfers: resp.capabilities.transfers,
        payouts_enabled: resp.payouts_enabled,
        charges_enabled: resp.charges_enabled,
        future_requirements: resp.future_requirements
      }

      if (needToCheckData.transfers === false || needToCheckData.payouts_enabled === false || needToCheckData.charges_enabled === false || needToCheckData.future_requirements.alternatives.length > 0 || needToCheckData.future_requirements.currently_due.length > 0 || needToCheckData.future_requirements.eventually_due.length > 0 || needToCheckData.future_requirements.past_due.length > 0 || needToCheckData.future_requirements.pending_verification.length > 0) {
        owner.stripeKycRequire = true
        owner.save()
      } else {
        owner.stripeKycRequire = false
        owner.save()
        throw new Error("Already done Kyc")
      }

      let { HOME_PAGE } = process.env
      if (owner.lastKYCSubmitted !== undefined) {
        HOME_PAGE = `${process.env.SITE_URL}/my-account`
      }
      console.log("HOME_PAGE", HOME_PAGE)
      const accountLink = await stripeService.getFreshKycLink(owner.vendorStripeId, HOME_PAGE)

      return res.json({
        error: false,
        kycLink: accountLink
      })
    } catch (error) {
      // eslint-disable-next-line no-console
      console.log("[ERROR]==>", error)
      return res.status(500).json({
        error: true,
        reason: error.message
      })
    }
  },

  /**
   * @swagger
   * /test/process-subscription-payment:
   *   post:
   *     summary: Test the processSubscriptionPayment function
   *     tags: [Testing]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - userId
   *               - subscriptionId
   *             properties:
   *               userId:
   *                 type: string
   *                 description: ID of the user
   *                 example: "60d21b4667d0d8992e610c85"
   *               subscriptionId:
   *                 type: string
   *                 description: ID of the subscription
   *                 example: "60d21b4667d0d8992e610c86"
   *     responses:
   *       200:
   *         description: Subscription payment processed successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 message:
   *                   type: string
   *                   example: "Subscription payment processing initiated"
   *       400:
   *         description: Bad request - missing required fields
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 reason:
   *                   type: string
   *                   example: "User ID and Subscription ID are required"
   *       500:
   *         description: Server error
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 reason:
   *                   type: string
   *                   example: "An error occurred while processing the subscription payment"
   */
  async testProcessSubscriptionPayment(req, res) {
    try {
      const userId = req.user._id
      const { subscriptionId } = req.body

      // Validate input
      if (!userId || !subscriptionId) {
        return res.status(400).json({
          error: true,
          reason: "User ID and Subscription ID are required"
        })
      }

      // Process the subscription payment
      // We're using a try/catch inside the try block to handle errors from the payment processor
      // but still return a 200 response to the client
      try {
        // Call the processSubscriptionPayment function
        await paymentProcessor.processSubscriptionPayment(userId, subscriptionId, agenda)

        return res.json({
          error: false,
          message: "Subscription payment processing initiated successfully"
        })
      } catch (processingError) {
        console.error("Error in payment processing:", processingError)

        return res.json({
          error: false,
          message: "Subscription payment processing initiated, but encountered issues",
          details: processingError.message
        })
      }
    } catch (error) {
      console.error("Error in testProcessSubscriptionPayment:", error)
      return res.status(500).json({
        error: true,
        reason: error.message || "An error occurred while processing the subscription payment"
      })
    }
  },

  /**
   * @swagger
   * /payment/vendor/earnings:
   *   get:
   *     summary: Fetch earnings for a vendor
   *     tags: [Payment]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: query
   *         name: vendorId
   *         schema:
   *           type: string
   *         description: Optional vendor ID. If not provided, uses the authenticated user's vendor ID.
   *       - in: query
   *         name: startDate
   *         schema:
   *           type: string
   *           format: date
   *         description: Optional start date for the earnings period (ISO format)
   *       - in: query
   *         name: endDate
   *         schema:
   *           type: string
   *           format: date
   *         description: Optional end date for the earnings period (ISO format)
   *       - in: query
   *         name: limit
   *         schema:
   *           type: integer
   *           minimum: 1
   *           maximum: 100
   *           default: 100
   *         description: Maximum number of transactions to return
   *     responses:
   *       200:
   *         description: Successfully retrieved vendor earnings
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     earnings:
   *                       type: object
   *                       properties:
   *                         balance:
   *                           type: object
   *                           properties:
   *                             available:
   *                               type: number
   *                               example: 1000.00
   *                             pending:
   *                               type: number
   *                               example: 500.00
   *                         earnings:
   *                           type: object
   *                           properties:
   *                             total:
   *                               type: number
   *                               example: 5000.00
   *                             currency:
   *                               type: string
   *                               example: "usd"
   *                             period:
   *                               type: object
   *                               properties:
   *                                 start:
   *                                   type: string
   *                                   format: date-time
   *                                 end:
   *                                   type: string
   *                                   format: date-time
   *                         transactions:
   *                           type: array
   *                           items:
   *                             type: object
   *                             properties:
   *                               id:
   *                                 type: string
   *                               amount:
   *                                 type: number
   *                               net:
   *                                 type: number
   *                               fee:
   *                                 type: number
   *                               type:
   *                                 type: string
   *                               created:
   *                                 type: number
   *                               description:
   *                                 type: string
   *                               status:
   *                                 type: string
   *                               currency:
   *                                 type: string
   *       400:
   *         description: Error fetching vendor earnings
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 reason:
   *                   type: string
   *                   example: "Vendor ID is required"
   *       404:
   *         description: Vendor not found
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 reason:
   *                   type: string
   *                   example: "Vendor not found"
   */
  async fetchVendorEarnings(req, res) {
    try {
      // Get vendorId from query params or use the authenticated user's vendorId
      let { vendorId } = req.query
      const { startDate, endDate, limit } = req.query

      // If vendorId is not provided, try to get it from the authenticated user
      if (!vendorId) {
        const owner = await Owner.findOne({ _id: req.user.id }).exec()

        if (!owner || !owner.vendorStripeId) {
          return res.status(404).json({
            error: true,
            reason: "Vendor not found. No vendor ID provided and authenticated user is not a vendor."
          })
        }

        vendorId = owner.vendorStripeId
      }

      // Prepare options for the fetchVendorEarnings function
      const options = {}

      if (startDate) {
        options.startDate = new Date(startDate)
      }

      if (endDate) {
        options.endDate = new Date(endDate)
      }

      if (limit) {
        options.limit = parseInt(limit, 10)
      }

      // Fetch vendor earnings
      const earnings = await stripeService.fetchVendorEarnings(vendorId, options)

      return res.json({
        error: false,
        data: {
          earnings
        }
      })
    } catch (error) {
      console.error("Error in fetchVendorEarnings:", error)
      return res.status(400).json({
        error: true,
        reason: error.message || "Error fetching vendor earnings"
      })
    }
  },
}
