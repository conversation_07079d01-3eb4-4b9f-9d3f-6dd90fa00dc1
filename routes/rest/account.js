const { User, Payment } = require("../../models")
const stripeService = require("../../lib/stripe")

module.exports = {
  /**
   * @swagger
   * /me:
   *   get:
   *     summary: Get account details
   *     tags: [Account]
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: Account details retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     user:
   *                       type: object
   *                       properties:
   *                         _owner:
   *                           type: string
   *                           example: "60d21b4667d0d8992e610c85"
   *                         username:
   *                           type: string
   *                           example: "johndoe"
   *                         name:
   *                           type: object
   *                           properties:
   *                             first:
   *                               type: string
   *                               example: "John"
   *                             last:
   *                               type: string
   *                               example: "Doe"
   *                         email:
   *                           type: string
   *                           example: "<EMAIL>"
   *                         accountType:
   *                           type: string
   *                           example: "email"
   *                         phone:
   *                           type: string
   *                           example: "**********"
   *                         address:
   *                           type: string
   *                           example: "123 Main St"
   *                         gender:
   *                           type: string
   *                           example: "Male"
   *                         isActive:
   *                           type: boolean
   *                           example: true
   *                         isEmailVerified:
   *                           type: boolean
   *                           example: true
   *                         stripeId:
   *                           type: string
   *                           example: "cus_**********"
   *                         vendorStripeId:
   *                           type: string
   *                           example: "acct_**********"
   *                         teamMembers:
   *                           type: array
   *                           items:
   *                             type: string
   *                             example: "60d21b4667d0d8992e610c85"
   *                         platforms:
   *                           type: array
   *                           items:
   *                             type: string
   *                             example: "60d21b4667d0d8992e610c85"
   *       404:
   *         description: User not found
   *       500:
   *         description: Server error
   */
  async get(req, res) {
    try {
      const userId = req.user.id

      const user = await User.findOne({ _id: userId })
        .select("firstName lastName email phone subscription websiteStatus launchStatus userType platforms")
        .lean()

      if (!user) {
        return res.status(404).json({
          error: true,
          reason: "User not found"
        })
      }

      return res.json({
        error: false,
        data: {
          user
        }
      })
    } catch (error) {
      return res.status(500).json({
        error: true,
        reason: error.message
      })
    }
  },

  /**
   * @swagger
   * /account:
   *   put:
   *     summary: Update account details
   *     tags: [Account]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             properties:
   *               firstName:
   *                 type: string
   *                 description: First name
   *               lastName:
   *                 type: string
   *                 description: Last name
   *               phone:
   *                 type: string
   *                 description: Phone number
   *           example:
   *             firstName: "John"
   *             lastName: "Doe"
   *             phone: "**********"
   *     responses:
   *       200:
   *         description: Account details updated successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 account:
   *                   type: object
   *                   properties:
   *                     firstName:
   *                       type: string
   *                       example: "John"
   *                     lastName:
   *                       type: string
   *                       example: "Doe"
   *                     email:
   *                       type: string
   *                       example: "<EMAIL>"
   *                     phone:
   *                       type: string
   *                       example: "**********"
   *       404:
   *         description: User not found
   *       500:
   *         description: Server error
   */
  async update(req, res) {
    try {
      const userId = req.user.id
      const { firstName, lastName, phone } = req.body

      // Get user
      const user = await User.findOne({ _id: userId })

      if (!user) {
        return res.status(404).json({
          error: true,
          reason: "User not found"
        })
      }

      // Update fields if provided
      if (firstName) user.firstName = firstName
      if (lastName) user.lastName = lastName
      if (phone) user.phone = phone

      await user.save()

      return res.json({
        error: false,
        account: {
          firstName: user.firstName,
          lastName: user.lastName,
          email: user.email,
          phone: user.phone
        }
      })
    } catch (error) {
      return res.status(500).json({
        error: true,
        reason: error.message
      })
    }
  },

  /**
   * @swagger
   * /account/payments:
   *   get:
   *     summary: Get payment history
   *     tags: [Account]
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: Payment history retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 payments:
   *                   type: array
   *                   items:
   *                     type: object
   *                     properties:
   *                       _id:
   *                         type: string
   *                         example: "60d21b4667d0d8992e610c85"
   *                       user:
   *                         type: string
   *                         example: "60d21b4667d0d8992e610c85"
   *                       type:
   *                         type: string
   *                         example: "setup-fee"
   *                       amount:
   *                         type: number
   *                         example: 450
   *                       status:
   *                         type: string
   *                         example: "completed"
   *                       createdAt:
   *                         type: string
   *                         format: date-time
   *                         example: "2023-01-01T00:00:00.000Z"
   *       500:
   *         description: Server error
   */
  async getPaymentHistory(req, res) {
    try {
      const userId = req.user.id

      const payments = await Payment.find({ _user: userId })
        .populate("_subscription", "model")
        .sort("-createdAt")
        .lean()
        .exec()

      return res.json({
        error: false,
        data: {
          payments
        }
      })
    } catch (error) {
      return res.status(500).json({
        error: true,
        reason: error.message
      })
    }
  },

  /**
   * @swagger
   * /account/payment-method:
   *   put:
   *     summary: Update payment method
   *     tags: [Account]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - paymentMethodId
   *             properties:
   *               paymentMethodId:
   *                 type: string
   *                 description: Stripe payment method ID
   *           example:
   *             paymentMethodId: "pm_123456789"
   *     responses:
   *       200:
   *         description: Payment method updated successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 message:
   *                   type: string
   *                   example: "Payment method updated successfully"
   *                 paymentMethods:
   *                   type: array
   *                   items:
   *                     type: object
   *                     properties:
   *                       id:
   *                         type: string
   *                         example: "pm_123456789"
   *                       card:
   *                         type: object
   *                         properties:
   *                           brand:
   *                             type: string
   *                             example: "visa"
   *                           last4:
   *                             type: string
   *                             example: "4242"
   *                           exp_month:
   *                             type: integer
   *                             example: 12
   *                           exp_year:
   *                             type: integer
   *                             example: 2025
   *       400:
   *         description: Bad request - missing payment method ID or no Stripe customer account
   *       404:
   *         description: User not found
   *       500:
   *         description: Server error
   */
  async updatePaymentMethod(req, res) {
    try {
      const userId = req.user.id
      const { paymentMethodId } = req.body

      if (!paymentMethodId) {
        return res.status(400).json({
          error: true,
          reason: "Payment method ID is required"
        })
      }

      // Get user
      const user = await User.findOne({ _id: userId })

      if (!user) {
        return res.status(404).json({
          error: true,
          reason: "User not found"
        })
      }

      // Check if user has a Stripe customer ID
      if (!user.subscription || !user.subscription.stripeId) {
        return res.status(400).json({
          error: true,
          reason: "No Stripe customer account found"
        })
      }

      // Add the payment method to the customer
      await stripeService.addPaymentMethod(
        user.subscription.stripeId,
        paymentMethodId
      )

      // Get all payment methods to return to the client
      const paymentMethods = await stripeService.getPaymentMethods(
        user.subscription.stripeId
      )

      return res.json({
        error: false,
        message: "Payment method updated successfully",
        paymentMethods
      })
    } catch (error) {
      console.error("Update payment method error:", error)
      return res.status(500).json({
        error: true,
        reason: error.message || "An error occurred while updating your payment method"
      })
    }
  },

  /**
   * @swagger
   * /account/payment-methods:
   *   get:
   *     summary: Get all payment methods
   *     tags: [Account]
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: Payment methods retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 paymentMethods:
   *                   type: array
   *                   items:
   *                     type: object
   *                     properties:
   *                       id:
   *                         type: string
   *                         example: "pm_123456789"
   *                       card:
   *                         type: object
   *                         properties:
   *                           brand:
   *                             type: string
   *                             example: "visa"
   *                           last4:
   *                             type: string
   *                             example: "4242"
   *                           exp_month:
   *                             type: integer
   *                             example: 12
   *                           exp_year:
   *                             type: integer
   *                             example: 2025
   *       400:
   *         description: Bad request - no Stripe customer account
   *       404:
   *         description: User not found
   *       500:
   *         description: Server error
   */
  async getPaymentMethods(req, res) {
    try {
      const userId = req.user.id

      // Get user
      const user = await User.findOne({ _id: userId })

      if (!user) {
        return res.status(404).json({
          error: true,
          reason: "User not found"
        })
      }

      // Check if user has a Stripe customer ID
      if (!user.subscription || !user.subscription.stripeId) {
        return res.status(400).json({
          error: true,
          reason: "No Stripe customer account found"
        })
      }

      // Get all payment methods
      const paymentMethods = await stripeService.getPaymentMethods(
        user.subscription.stripeId
      )

      return res.json({
        error: false,
        paymentMethods
      })
    } catch (error) {
      console.error("Get payment methods error:", error)
      return res.status(500).json({
        error: true,
        reason: error.message || "An error occurred while retrieving your payment methods"
      })
    }
  },

  /**
   * @swagger
   * /account/payment-method/{id}:
   *   delete:
   *     summary: Delete a payment method
   *     tags: [Account]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Payment method ID
   *     responses:
   *       200:
   *         description: Payment method deleted successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 message:
   *                   type: string
   *                   example: "Payment method deleted successfully"
   *                 paymentMethods:
   *                   type: array
   *                   items:
   *                     type: object
   *       400:
   *         description: Bad request - missing payment method ID or no Stripe customer account
   *       404:
   *         description: User not found
   *       500:
   *         description: Server error
   */
  async deletePaymentMethod(req, res) {
    try {
      const userId = req.user.id
      const { id } = req.params

      if (!id) {
        return res.status(400).json({
          error: true,
          reason: "Payment method ID is required"
        })
      }

      // Get user
      const user = await User.findOne({ _id: userId })

      if (!user) {
        return res.status(404).json({
          error: true,
          reason: "User not found"
        })
      }

      // Check if user has a Stripe customer ID
      if (!user.subscription || !user.subscription.stripeId) {
        return res.status(400).json({
          error: true,
          reason: "No Stripe customer account found"
        })
      }

      // Delete the payment method
      await stripeService.deletePaymentMethod(id)

      // Get updated list of payment methods
      const paymentMethods = await stripeService.getPaymentMethods(
        user.subscription.stripeId
      )

      return res.json({
        error: false,
        message: "Payment method deleted successfully",
        paymentMethods
      })
    } catch (error) {
      console.error("Delete payment method error:", error)
      return res.status(500).json({
        error: true,
        reason: error.message || "An error occurred while deleting your payment method"
      })
    }
  },

  /**
   * @swagger
   * /account/payment-method/{id}/default:
   *   put:
   *     summary: Set a payment method as default
   *     tags: [Account]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Payment method ID
   *     responses:
   *       200:
   *         description: Default payment method updated successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 message:
   *                   type: string
   *                   example: "Default payment method updated successfully"
   *                 paymentMethods:
   *                   type: array
   *                   items:
   *                     type: object
   *                     properties:
   *                       id:
   *                         type: string
   *                         example: "pm_123456789"
   *                       card:
   *                         type: object
   *                         properties:
   *                           brand:
   *                             type: string
   *                             example: "visa"
   *                           last4:
   *                             type: string
   *                             example: "4242"
   *                           exp_month:
   *                             type: integer
   *                             example: 12
   *                           exp_year:
   *                             type: integer
   *                             example: 2025
   *       400:
   *         description: Bad request - missing payment method ID or no Stripe customer account
   *       404:
   *         description: User not found
   *       500:
   *         description: Server error
   */
  async setDefaultPaymentMethod(req, res) {
    try {
      const userId = req.user.id
      const { id } = req.params

      if (!id) {
        return res.status(400).json({
          error: true,
          reason: "Payment method ID is required"
        })
      }

      // Get user
      const user = await User.findOne({ _id: userId })

      if (!user) {
        return res.status(404).json({
          error: true,
          reason: "User not found"
        })
      }

      // Check if user has a Stripe customer ID
      if (!user.subscription || !user.subscription.stripeId) {
        return res.status(400).json({
          error: true,
          reason: "No Stripe customer account found"
        })
      }

      // Set as default payment method
      await stripeService.setDefaultPaymentMethod(
        user.subscription.stripeId,
        id
      )

      // Get updated list of payment methods
      const paymentMethods = await stripeService.getPaymentMethods(
        user.subscription.stripeId
      )

      return res.json({
        error: false,
        message: "Default payment method updated successfully",
        paymentMethods
      })
    } catch (error) {
      console.error("Set default payment method error:", error)
      return res.status(500).json({
        error: true,
        reason: error.message || "An error occurred while updating your default payment method"
      })
    }
  }
}
