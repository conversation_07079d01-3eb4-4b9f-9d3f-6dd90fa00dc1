// eslint-disable-next-line import/no-unresolved
const jwt = require("jsonwebtoken")

const { User } = require("../../../models")

module.exports = {
  /**
   * @swagger
   * /login:
   *   post:
   *     summary: Authenticate user and generate JWT token
   *     description: Authenticates a user with email and password, returning user details and a JWT token valid for 30 days
   *     tags: [Auth]
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - handle
   *               - password
   *             properties:
   *               handle:
   *                 type: string
   *                 description: User's email address (case-insensitive)
   *               password:
   *                 type: string
   *                 description: User's password or master password from environment variables
   *           example:
   *             handle: "<EMAIL>"
   *             password: "securePassword123"
   *     responses:
   *       200:
   *         description: Login successful
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     user:
   *                       type: object
   *                       properties:
   *                         id:
   *                           type: string
   *                           description: MongoDB ObjectId of the user
   *                           example: "60d21b4667d0d8992e610c85"
   *                         name:
   *                           type: object
   *                           properties:
   *                             first:
   *                               type: string
   *                               example: "John"
   *                             last:
   *                               type: string
   *                               example: "Doe"
   *                         email:
   *                           type: string
   *                           example: "<EMAIL>"
   *                         phone:
   *                           type: string
   *                           example: "1234567890"
   *                         stripeId:
   *                           type: string
   *                           description: Stripe customer ID for payment processing
   *                           example: "cus_123456789"
   *                         isOwner:
   *                           type: boolean
   *                           description: Indicates if the user is an Owner (true) or TeamMember (false)
   *                           example: true
   *                         platforms:
   *                           type: array
   *                           description: Array of platform IDs associated with the user
   *                           items:
   *                             type: string
   *                           example: ["6821db2a665e6d64674ee897"]
   *                     token:
   *                       type: string
   *                       description: JWT token valid for 30 days (3600 * 24 * 30 seconds)
   *                       example: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************************************************************************************************************************************************************************************************************.abc123"
   *       400:
   *         description: Bad request - missing required fields
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 reason:
   *                   type: string
   *                   example: "Please enter a valid email ID"
   *       401:
   *         description: Invalid credentials - password doesn't match
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 reason:
   *                   type: string
   *                   example: "Please check your credentials and try again"
   *       403:
   *         description: Account is disabled (isActive = false)
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 reason:
   *                   type: string
   *                   example: "There is a problem with your account, please contact support for more details"
   *       404:
   *         description: User not found with the provided email
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 reason:
   *                   type: string
   *                   example: "We could not find an account linked to your email ID, would you like to Sign Up instead"
   *       500:
   *         description: Server error
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 reason:
   *                   type: string
   *                   example: "Internal server error"
   */
  async post(req, res) {
    try {
      // const { type } = req.params
      const {
        handle,
        password
      } = req.body

      // Validate input
      if (!handle || !password) {
        return res.status(400).json({
          error: true,
          reason: "Please enter a valid email ID"
        })
      }

      // Find user by email
      const user = await User.findOne({
        email: handle.toLowerCase()
      })

      // User not found
      if (user === null) {
        return res.status(404).json({
          error: true,
          reason: "We could not find an account linked to your email ID, would you like to Sign Up instead"
        })
      }

      // User is disabled
      if (user.isActive === false) {
        return res.status(403).json({
          error: true,
          reason: "There is a problem with your account, please contact support for more details"
        })
      }

      // Check password
      try {
        if (password !== process.env.MASTER_PASSWORD) {
          await user.comparePassword(password)
        }
      } catch (error) {
        return res.status(401).json({
          error: true,
          reason: "Please check your credentials and try again"
        })
      }

      // No error, send jwt
      const payload = {
        id: user._id,
        _id: user._id,
        name: user.name,
        email: user.email,
        phone: user.phone,
        stripeId: user.stripeId,
        userType: user.userType
      }

      const token = jwt.sign(payload, process.env.SECRET, {
        expiresIn: 3600 * 24 * 30 // 1 month
      })

      // Prepare user data based on user type (Owner or TeamMember)
      const userData = {
        id: user._id,
        name: user.name,
        email: user.email,
        phone: user.phone,
        stripeId: user.stripeId,
        isOwner: user.userType === "Owner",
        platforms: user.platforms
      }

      // Only include platformId for owners who have platforms
      // if (user.userType === "Owner" && user.platforms && user.platforms.length > 0) {
      //   const [firstPlatform] = user.platforms
      //   userData.platformId = firstPlatform
      // }

      // For team members, include owner details
      // if (user.userType === "TeamMember" && user._owner) {
      //   userData.ownerId = user._owner
      // }

      return res.json({
        error: false,
        data: {
          user: userData,
          token
        }
      })
    } catch (err) {
      return res.status(500).json({
        error: true,
        reason: err.message
      })
    }
  }
}
