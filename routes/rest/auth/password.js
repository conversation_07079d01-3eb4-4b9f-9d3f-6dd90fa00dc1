const moment = require("moment")
const cuid = require("cuid")

const mail = require("../../../lib/mail")

const { User } = require("../../../models")

module.exports = {
  /**
   * @swagger
   * /forgotpassword:
   *   post:
   *     summary: Request password reset link
   *     description: Sends a password reset link to the user's email. In development environment, returns the reset link directly in the response.
   *     tags: [Auth]
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - handle
   *             properties:
   *               handle:
   *                 type: string
   *                 format: email
   *                 description: User's email address
   *           examples:
   *             standard_request:
   *               summary: Standard password reset request
   *               value:
   *                 handle: "<EMAIL>"
   *             alternate_email:
   *               summary: Alternative email format
   *               value:
   *                 handle: "<EMAIL>"
   *     responses:
   *       200:
   *         description: Password reset instructions sent
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     message:
   *                       type: string
   *                       example: "Password reset instructions have been sent to your email"
   *                     resetUrl:
   *                       type: string
   *                       description: Only returned in development environment
   *                       example: "http://localhost:3000/resetpassword/abc123def456"
   *                     token:
   *                       type: string
   *                       description: Only returned in development environment
   *                       example: "abc123def456"
   *                     email:
   *                       type: string
   *                       description: Only returned in development environment
   *                       example: "<EMAIL>"
   *             examples:
   *               production_response:
   *                 summary: Production environment response
   *                 value:
   *                   error: false
   *                   data:
   *                     message: "Password reset instructions have been sent to your email"
   *               development_response:
   *                 summary: Development environment response (includes reset link)
   *                 value:
   *                   error: false
   *                   data:
   *                     message: "Development mode: Password reset link generated (email not sent)"
   *                     resetUrl: "http://localhost:3000/resetpassword/abc123def456"
   *                     token: "abc123def456"
   *                     email: "<EMAIL>"
   *       403:
   *         description: Account is inactive
   *       404:
   *         description: User not found
   *       500:
   *         description: Server error
   */
  async startWorkflow(req, res) {
    try {
      const { handle } = req.body

      // Find user by email
      const user = await User.findOne({ email: handle })

      // User not found
      if (user === null) {
        return res.status(404).json({
          error: true,
          reason: "No active user found linked to this email address"
        })
      }

      // User is inactive
      if (!user.isActive) {
        return res.status(403).json({
          error: true,
          reason: "There is a problem with your account, please contact support for more details"
        })
      }

      // Generate reset token
      const now = Date.now()
      const token = cuid.slug()

      // Set token expiration to 4 hours as per SRS
      user.forgotpassword = {
        requestedAt: now,
        token,
        expiresAt: moment(now)
          .add(4, "hours")
          .toDate()
      }

      await user.save()

      const resetUrl = `${process.env.SITE_URL}/resetpassword/${token}`

      // Send the password reset email
      try {
        await mail("forgot-password", {
          to: user.email,
          subject: "Password Reset Request",
          locals: {
            firstName: user.name.first,
            url: resetUrl,
            platformName: "Vewmee White Level"
          }
        })
      } catch (mailErr) {
        console.log("==> Mail sending Error: ", mailErr)
        throw new Error(
          "Failed to send Password Reset Email! Please Retry Later."
        )
      }

      return res.status(200).json({
        error: false,
        data: {
          message: "Password reset instructions have been sent to your email"
        }
      })
    } catch (error) {
      return res.status(500).json({ error: true, reason: error.message })
    }
  },

  /**
   * @swagger
   * /resetpassword:
   *   post:
   *     summary: Reset password with token
   *     description: Sets a new password using the token received via email or from the forgotpassword endpoint
   *     tags: [Auth]
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             type: object
   *             required:
   *               - token
   *               - password
   *               - email
   *             properties:
   *               token:
   *                 type: string
   *                 description: Password reset token received via email or from the forgotpassword endpoint
   *               password:
   *                 type: string
   *                 format: password
   *                 description: New password (min 8 characters recommended)
   *               email:
   *                 type: string
   *                 format: email
   *                 description: User's email address
   *           examples:
   *             standard_reset:
   *               summary: Standard password reset
   *               value:
   *                 token: "abc123def456"
   *                 password: "newSecurePassword123"
   *                 email: "<EMAIL>"
   *             complex_password:
   *               summary: Reset with complex password
   *               value:
   *                 token: "xyz789uvw012"
   *                 password: "C0mpl3x!P@ssw0rd"
   *                 email: "<EMAIL>"
   *     responses:
   *       200:
   *         description: Password reset successful
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     message:
   *                       type: string
   *                       example: "Password has been reset successfully"
   *             examples:
   *               success_response:
   *                 summary: Successful password reset
   *                 value:
   *                   error: false
   *                   data:
   *                     message: "Password has been reset successfully"
   *       400:
   *         description: Invalid or expired token
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 reason:
   *                   type: string
   *                   example: "Token expired"
   *             example:
   *               error: true
   *               reason: "Token expired"
   *       403:
   *         description: Account is inactive
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 reason:
   *                   type: string
   *                   example: "Try to request a new password reset token"
   *             example:
   *               error: true
   *               reason: "Try to request a new password reset token"
   *       500:
   *         description: Server error
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: true
   *                 reason:
   *                   type: string
   *                   example: "Internal server error"
   *             example:
   *               error: true
   *               reason: "Internal server error"
   */
  async resetPassword(req, res) {
    try {
      const { token, password, email } = req.body
      console.log(req.body)
      // Find user with valid token
      const user = await User.findOne({
        "forgotpassword.token": token,
        "forgotpassword.expiresAt": { $gte: new Date() },
        email
      })

      // Token invalid or expired
      if (user === null) {
        return res.status(400).json({
          error: true,
          reason: "Token expired"
        })
      }

      // User is inactive
      if (!user.isActive) {
        return res.status(403).json({
          error: true,
          reason: "Try to request a new password reset token"
        })
      }

      // Update password
      user.password = password
      user.lastModifiedAt = Date.now()

      // Clear token (mark as used)
      user.forgotpassword = {
        requestedAt: null,
        token: null,
        expiresAt: null
      }

      await user.save()

      return res.status(200).json({
        error: false,
        data: {
          message: "Password has been reset successfully"
        }
      })
    } catch (error) {
      return res.status(500).json({ error: true, reason: error.message })
    }
  }
}
