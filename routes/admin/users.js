const { User } = require("../../models")

module.exports = {
  /**
   * @swagger
   * /user/{id}:
   *   get:
   *     summary: Get admin user details
   *     tags: [Admin-User]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *       - in: path
   *         name: id
   *         required: true
   *         schema:
   *           type: string
   *         description: Admin user's unique ID
   *     responses:
   *       200:
   *         description: Admin user details retrieved successfully
   *         content:
   *           application/json:
   *             schema:
   *               type: object
   *               properties:
   *                 error:
   *                   type: boolean
   *                   example: false
   *                 data:
   *                   type: object
   *                   properties:
   *                     user:
   *                       type: object
   *                       properties:
   *                         _id:
   *                           type: string
   *                           example: "60d21b4667d0d8992e610c85"
   *                         email:
   *                           type: string
   *                           example: "<EMAIL>"
   *                         phone:
   *                           type: string
   *                           example: "00000000000"
   *                         name:
   *                           type: object
   *                           properties:
   *                             first:
   *                               type: string
   *                               example: "Jhon"
   *                             last:
   *                               type: string
   *                               example: "Doe"
   *                         isAdmin:
   *                           type: boolean
   *                           example: true
   *       404:
   *         description: Admin user not found
   *       500:
   *         description: Server error
   */
  async get(req, res) {
    try {
      const {
        id
      } = req.params
      const user = await User.findOne({
        _id: id,
        isAdmin: true
      })
        .select("-password -forgotpassword")
        .exec()
      if (user === null) throw new Error("No admin user found for the given id")

      return res.json({
        error: false,
        data: {
          user
        }
      })
    } catch (err) {
      return res.status(500).json({
        error: true,
        reason: err.message
      })
    }
  }
}
