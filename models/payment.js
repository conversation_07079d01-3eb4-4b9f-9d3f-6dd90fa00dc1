const mongoose = require("mongoose")

const paymentSchema = new mongoose.Schema({
  paymentIntentId: {
    type: String,
  },
  amount: {
    type: Number,
    required: true
  },
  currency: {
    type: String,
    required: true,
    default: "USD"
  },
  status: {
    type: String,
    enum: ["pending", "completed", "failed", "canceled"],
    default: "pending"
  },
  paymentMethod: {
    type: String,
    // required: true
  },
  //   transactionId: {
  //     type: String,
  //     unique: true
  //   },
  _user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
    required: true
  },
  metadata: {
    type: Object
  },
  paymentFor: {
    type: String,
    enum: ["domain", "subscription-setup", "subscription-renewal"],
    required: true
  },
  netPayout: {
    type: Number
  },
  totalDeducted: {
    type: Number
  },
  processingFee: {
    type: Number
  },
  platformFee: {
    type: Number
  },
  _platform: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Platform",
    required: true
  },
  _subscription: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Subscription"
  }
}, {
  timestamps: true
})

module.exports = mongoose.model("Payment", paymentSchema)
