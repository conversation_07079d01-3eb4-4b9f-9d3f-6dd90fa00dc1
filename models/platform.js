const mongoose = require("mongoose")
const { Schema } = mongoose

const PlatformSchema = new mongoose.Schema({
  // Primary user for this platform (owner)
  primaryUser: {
    type: Schema.Types.ObjectId,
    ref: "User",
    required: true
  },

  // Team members for this platform
  teamMembers: [{
    type: Schema.Types.ObjectId,
    ref: "User"
  }],

  // Platform information
  name: {
    type: String,
    minlength: 2,
    maxlength: 50
  },

  colorPalette: {
    type: String
  },

  logo: {
    type: String // URL to the logo image
  },

  socialProfiles: {
    facebook: String,
    twitter: String,
    instagram: String,
    other: String
  },

  ideaDescription: {
    type: String,
    maxlength: 300
  },

  existingWebsite: String,

  specialRequirements: {
    type: String,
    maxlength: 300
  },

  // Reference to subscription
  subscription: {
    type: Schema.Types.ObjectId,
    ref: "Subscription"
  },

  // Domain information
  domain: {
    name: String,
    provider: String,
    verified: {
      type: Boolean,
      default: false
    },
    dnsRecords: [{
      recordType: {
        type: String
      },
      host: {
        type: String
      },
      value: {
        type: String
      },
      verified: {
        type: Boolean,
        default: false
      },
      ttl: {
        type: Number
      },
      purpose: {
        type: String
      }
    }],
    certificates: [{
      certificateArn: {
        type: String
      },
      subDomain: {
        type: String
      },
      issuedAt: {
        type: Date,
        default: Date.now
      },
      expiresAt: {
        type: Date
      }
    }],
    verificationToken: String
  },

  // Launch status
  launchStatus: {
    type: Number,
    default: 1
  },

  // Website status
  websiteStatus: {
    type: String,
    enum: ["inactive", "active", "suspended", "deleted"],
    default: "inactive"
  },

  // Platform links
  website: {
    type: String
  },

  fanAppLink: {
    type: String
  },

  creatorLink: {
    type: String
  }
}, { timestamps: true })

// Add virtual fields for easy access
PlatformSchema.virtual("hasSubscription").get(function () {
  return !!this.subscription
})

PlatformSchema.virtual("hasDomain").get(function () {
  return !!(this.domain && this.domain.verified)
})

// Set options for the schema
PlatformSchema.set("toJSON", { virtuals: true })
PlatformSchema.set("toObject", { virtuals: true })

module.exports = mongoose.model("Platform", PlatformSchema)
