const mongoose = require("mongoose")
const { Schema } = mongoose

const DocumentSchema = new Schema({
  // Document type (FAQs, Terms & Conditions, etc.)
  type: {
    type: String,
    enum: ["faqs", "terms_and_conditions", "privacy_policy", "refund_policy", "feedback", "other"],
    required: true
  },

  // AWS URL to the document
  documentUrl: {
    type: String,
    required: true
  },

  // Content for suggestions or feedback
  content: {
    type: String,
    maxlength: 1000
  },

  // Status of the content (suggestion or feedback)
  status: {
    type: String,
    enum: ["active", "resolved"],
    default: "active"
  },

  // User who added the suggestion
  addedBy: {
    type: Schema.Types.ObjectId,
    ref: "User"
  },

  _platform: {
    type: Schema.Types.ObjectId,
    ref: "Platform"
  }
}, { timestamps: true })

// Add virtual for checking if content exists
DocumentSchema.virtual("hasContent").get(function () {
  return this.content && this.content.trim().length > 0
})

// For backward compatibility
DocumentSchema.virtual("suggestion").get(function () {
  return this.content
})
DocumentSchema.virtual("suggestionStatus").get(function () {
  return this.status
})

// Set options for the schema
DocumentSchema.set("toJSON", { virtuals: true })
DocumentSchema.set("toObject", { virtuals: true })

module.exports = mongoose.model("Document", DocumentSchema)
