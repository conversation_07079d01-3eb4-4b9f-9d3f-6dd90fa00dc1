const mongoose = require("mongoose")
const { Schema } = mongoose

const SubscriptionSchema = new Schema({
  user: {
    type: Schema.Types.ObjectId,
    ref: "User",
    required: true
  },
  status: {
    type: String,
    enum: ["inactive", "active", "expired", "cancelled", "past_due", "unpaid"],
    default: "inactive"
  },
  setupFeePaid: {
    type: Boolean,
    default: false
  },
  startDate: Date,
  endDate: Date,
  currentPeriodStart: Date,
  currentPeriodEnd: Date,
  model: {
    type: String,
    enum: ["revenue-share", "flat-fee", "setup-fee"],
    default: "setup-fee"
  },
  stripeId: String,

  defaultPaymentMethodId: String,
  trialEnd: Date,
  canceledAt: Date,
  earnings: Number,
  _platform: {
    type: Schema.Types.ObjectId,
    ref: "Platform",
    required: true
  }
}, { timestamps: true })

module.exports = mongoose.model("Subscription", SubscriptionSchema)
