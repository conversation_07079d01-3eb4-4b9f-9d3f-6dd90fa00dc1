const mongoose = require("mongoose")
const { Schema } = mongoose

const SettingSchema = new Schema({
  revenueSharePercentage: {
    type: Number,
    default: 10 // 10% revenue share
  },
  flatFeeAmount: {
    type: Number,
    default: 150 // $150 flat fee
  },
  processingFeePercentage: {
    type: Number,
    default: 5 // 5% processing fee
  },
  monthlyRevenueThreshold: {
    type: Number,
    default: 1500 // $1,500 threshold for revenue share
  }
}, { timestamps: true })

module.exports = mongoose.model("Setting", SettingSchema)
