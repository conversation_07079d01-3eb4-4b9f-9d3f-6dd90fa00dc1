const mongoose = require("mongoose")
const { Schema } = mongoose

const CommentSchema = new Schema({
  user: {
    type: Schema.Types.ObjectId,
    ref: "User",
    required: true
  },
  content: {
    type: String,
    required: true,
    maxlength: 500
  },
  section: {
    type: String,
    enum: ["Creator Login", "Creator Sign Up", "Fan Website", "Fan Sign Up", "Fan Login", "Fan Feed"]
  },
  platform: {
    type: Schema.Types.ObjectId,
    ref: "Platform",
    required: true
  },
  parentComment: {
    type: Schema.Types.ObjectId,
    ref: "Comment",
    default: null
  },
  replies: [{
    type: Schema.Types.ObjectId,
    ref: "Comment"
  }],
  isResolved: {
    type: Boolean,
    default: false
  }
}, { timestamps: true })

module.exports = mongoose.model("Comment", CommentSchema)
