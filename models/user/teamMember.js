/* eslint-disable max-len */
const mongoose = require("mongoose")
const User = require(".")

const TeamMemberSchema = new mongoose.Schema({
  _owner: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User"
  },
  // _platform: [{
  //   type: mongoose.Schema.Types.ObjectId,
  //   ref: "Platform"
  // }]
})

TeamMemberSchema.set("timestamps", true)
TeamMemberSchema.set("toJSON", { virtuals: true })
TeamMemberSchema.set("toObject", { virtuals: true })

module.exports = User.discriminator("TeamMember", TeamMemberSchema)
