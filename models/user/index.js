/* eslint-disable max-len */
/* eslint-disable no-useless-catch */
const mongoose = require("mongoose")
const bcrypt = require("bcrypt")
const randomstring = require("randomstring")

const mailer = require("../../lib/mail")

const UserSchema = new mongoose.Schema({
  username: {
    type: String
  },

  password: {
    type: String
  },

  name: {
    first: String,
    last: String
  },

  email: {
    type: String,
    lowercase: true,
    // unique: true,
  },

  accountType: {
    type: String,
    enum: ["google", "email", "fb"],
    default: "email"
  },

  socialId: String,

  phone: {
    type: String
  },

  address: {
    type: String
  },

  imageUrl: {
    type: String
  },

  gender: {
    type: String,
    enum: ["Male", "Female", "Other"],
  },

  isActive: {
    type: Boolean,
    default: false
  },

  forgotpassword: {
    requestedAt: { type: Date, default: null },
    token: { type: String, default: null },
    expiresAt: { type: Date, default: null }
  },

  isEmailVerified: {
    type: <PERSON><PERSON><PERSON>,
    default: false
  },

  userType: {
    type: String,
    enum: ["Owner", "TeamMember"],
  }
}, { timestamps: true })

UserSchema.pre("validate", function (next) {
  if (this.isNew && this.accountType !== "google") {
    if (this.password === undefined || this.password === null) {
      this.generatedPassword = randomstring.generate(8) // for usage in post save hook to send welcome email
      this.password = this.generatedPassword
    }
  }
  return next()
})

// Hash & save user's password:
UserSchema.pre("save", async function (next) {
  const user = this
  if ((this.isModified("password") || this.isNew) && this.accountType !== "google") {
    try {
      user.password = await bcrypt.hash(user.password, +process.env.SALT_ROUNDS || 10)
    } catch (error) {
      return next(error)
    }
  }
  return next()
})

// compare two passwords:
UserSchema.methods.comparePassword = async function (pw) {
  try {
    const isMatch = await bcrypt.compare(pw, this.password)
    if (isMatch === false) throw new Error("Please check your credentials and try again")
  } catch (error) {
    throw error // rethrow
  }
}
// eslint-disable-next-line prefer-arrow-callback
UserSchema.post("save", function (doc) {
  if (doc.generatedPassword !== undefined) {
    // Send welcome email, but NO WAITING!
    mailer("welcome", {
      to: doc.email,
      subject: "Welcome!!!",
      locals: { email: doc.email, password: doc.generatedPassword, name: doc.name }
    })
  }
})

UserSchema.virtual("fullName").get(function () {
  const first = (this.name && this.name.first) ? this.name.first : ""
  const last = (this.name && this.name.last) ? ` ${this.name.last}` : ""
  return `${first}${last}`
})

UserSchema.virtual("fullName").set(function (v) {
  if (!this.name) this.name = {}
  const spaceIndex = v.indexOf(" ")
  if (spaceIndex > 0) {
    this.name.first = v.substr(0, spaceIndex)
    this.name.last = v.substr(spaceIndex + 1)
  } else {
    this.name.first = v
    this.name.last = ""
  }
})

UserSchema.virtual("isOwner").get(function () {
  return this.userType === "Owner" // Check userType field to determine if user is an owner
})

UserSchema.set("timestamps", true)
UserSchema.set("toJSON", { virtuals: true })
UserSchema.set("toObject", { virtuals: true })

UserSchema.set("discriminatorKey", "userType")

module.exports = mongoose.model("User", UserSchema)
