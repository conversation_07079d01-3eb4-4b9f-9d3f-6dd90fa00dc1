/* eslint-disable max-len */
const mongoose = require("mongoose")
const User = require(".")

const OwnerSchema = new mongoose.Schema({

  stripeId: String,

  // Reference to platforms
  platforms: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: "Platform"
  }],

  // Cards information
  cards: [{
    id: String, // Stripe card ID
    last4: String,
    brand: String,
    expMonth: Number,
    expYear: Number,
    name: String,
    isDefault: {
      type: Boolean,
      default: false
    }
  }],

  vendorStripeId: String,

  stripeKycRequire: {
    type: Boolean,
    default: true
  }
})

OwnerSchema.set("timestamps", true)
OwnerSchema.set("toJSON", { virtuals: true })
OwnerSchema.set("toObject", { virtuals: true })

module.exports = User.discriminator("Owner", OwnerSchema)
